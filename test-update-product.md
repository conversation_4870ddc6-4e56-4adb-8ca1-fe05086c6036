# 产品更新功能测试

## 问题描述
解决了 `org.springframework.dao.DataIntegrityViolationException` 错误：
- 错误信息：`Field 'ref_table_ref_num' doesn't have a default value`
- 原因：在审计日志记录时，使用了不完整的产品对象

## 修复内容

### 1. 修改了 ProductUpdateParam 类
- 添加了 `supplierSku` 和 `upc` 字段
- 添加了 `@UpperCase` 注解确保数据一致性

### 2. 扩展了 ProductVersionService 接口
- 添加了 `updateSupplierSkuAndUpc` 方法

### 3. 实现了 ProductVersionServiceImpl 中的新方法
- 同步更新所有产品版本中的 supplierSku 和 upc
- 清理相关缓存

### 4. 扩展了 ProductScanService 接口
- 添加了 `updateScanDataForProduct` 方法

### 5. 实现了 ProductScanServiceImpl 中的新方法
- 同步更新扫描表中的 SKU 和 UPC 数据

### 6. 修复了 ProductServiceImpl.updateByParam 方法
- **关键修复**：使用完整的产品对象 `newProduct` 而不是转换后的 `entity` 来记录审计日志
- 添加了 UPC 和 SKU 的唯一性校验
- 添加了同步更新版本和扫描表的逻辑

## 关键修复点

### 问题根源
```java
// 错误的做法 - entity 对象可能缺少 refNum 等关键字段
ProductAuditLogHelper.recordLog(
    entity,  // 这个对象可能不完整
    ProductLogStatusEnum.CHANGE.getStatus(),
    BaseTypeLogEnum.BASE_INFO.getType(),
    ModifyCompareUtil.recordModifyLog(newProduct, oldProduct)
);
```

### 修复方案
```java
// 正确的做法 - 使用完整的产品对象
ProductAuditLogHelper.recordLog(
    newProduct,  // 使用完整的产品对象而不是转换后的entity
    ProductLogStatusEnum.CHANGE.getStatus(),
    BaseTypeLogEnum.BASE_INFO.getType(),
    ModifyCompareUtil.recordModifyLog(newProduct, oldProduct)
);
```

## 测试用例

### 基本更新测试
```java
ProductUpdateParam updateParam = new ProductUpdateParam();
updateParam.setId(productId);
updateParam.setSupplierSku("NEW_SKU_123");
updateParam.setUpc("NEW_UPC_456");
updateParam.setTitle("Updated Title");

Product updatedProduct = productService.updateByParam(updateParam);
```

### 预期结果
1. 主产品表更新成功
2. 所有产品版本的 supplierSku 和 upc 同步更新
3. 扫描表中相关记录同步更新
4. 审计日志正常记录，不再出现 `ref_table_ref_num` 错误
5. 缓存正确清理

## 数据一致性保证
- 事务性：所有更新在同一事务中执行
- 唯一性校验：确保 UPC 和 SKU 在同一合作伙伴下唯一
- 业务规则：UPC 和 SKU 不能相同
- 缓存管理：更新后自动清理相关缓存

## 审计日志功能

### 自动记录字段变化
当产品的 `supplierSku` 或 `upc` 字段发生变化时，系统会自动在 `auditshowlog` 表中记录变化：

1. **ModifyCompareUtil.recordModifyLog()** 方法会自动比较新旧产品对象的所有属性
2. 对于发生变化的字段，会生成 `ModificationLogVO` 对象
3. 变化记录会以 JSON 格式存储在审计日志的 `change_detail` 字段中

### 审计日志记录格式
```json
[
  {
    "fieldName": "supplierSku",
    "newValue": "NEW_SKU_123",
    "oldValue": "OLD_SKU_456"
  },
  {
    "fieldName": "upc",
    "newValue": "NEW_UPC_789",
    "oldValue": "OLD_UPC_012"
  }
]
```

### 审计日志字段说明
- `ref_table_ref_num`: 产品的 refNum（唯一标识）
- `ref_table_show_ref_num`: 产品的显示 refNum
- `log_status`: 日志状态（CHANGE - 变更）
- `log_type`: 日志类型（BASE_INFO - 基础信息）
- `change_detail`: 变化详情（JSON 格式）

### 新增常量定义
在 `ProductStatusFieldConstant` 类中添加了新的常量：
```java
public static final String SUPPLIER_SKU = "supplierSku";
public static final String UPC = "upc";
```

### 验证审计日志
可以通过查询 `auditshowlog` 表来验证变化是否被正确记录：
```sql
SELECT * FROM auditshowlog
WHERE ref_table_ref_num = 'PRODUCT_REF_NUM'
AND log_status = 'CHANGE'
AND log_type = 'BASE_INFO'
ORDER BY create_time DESC;
```
