package cn.need.cloud.apicenter.frp.handler;

import cn.need.cloud.apicenter.frp.client.constant.enums.ThirdSystem;
import cn.need.cloud.apicenter.frp.config.ApiHostConfig;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecord;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecordRequest;
import cn.need.cloud.apicenter.frp.model.entity.ThirdRecordResponse;
import cn.need.cloud.apicenter.frp.service.ThirdRecordRequestService;
import cn.need.cloud.apicenter.frp.service.ThirdRecordResponseService;
import cn.need.cloud.apicenter.frp.service.ThirdRecordService;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.UUIDUtil;
import cn.need.framework.common.core.map.Maps;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.support.rest.RestHelper;
import cn.need.framework.common.support.thread.ThreadUtil;
import cn.need.framework.common.support.util.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static cn.need.framework.common.core.lang.ObjectUtil.isNull;
import static cn.need.framework.common.core.lang.ObjectUtil.nullToDefault;

/**
 * 公共http接口调用处理抽象类
 *
 * <AUTHOR>
 * @since 2024/3/22
 */
@Slf4j
public abstract class AbstractHttpHandler {

    private final HttpHeaders headers = new HttpHeaders();
    private final Map<String, Object> paramMap = Maps.hashMap();
    protected String url;
    protected HttpMethod httpMethod;
    protected HttpEntity<?> httpEntity;
    protected RestTemplate restTemplate;
    private String host;
    @Getter
    private String path;
    private String token;
    private Object params;
    private ApiHostConfig apiHostConfig;
    private ThirdRecord record;
    private ThirdRecordRequest recordRequest;
    private ThirdRecordResponse recordResponse;

    public JSONObject request() {
        // 初始化
        initialize();
        // 前置处理
        preHandle();

        JSONObject response = null;
        try {
            response = this.executeExchange();
        } catch (HttpClientErrorException e) {
            // 如果请求报401错误，刷新token后重试（如有其他token过期情况，再进行改写）
            if (Objects.equals(e.getRawStatusCode(), HttpStatus.UNAUTHORIZED.value())) {
                refreshToken();
                response = this.executeExchange();
            }
            //} catch (Exception e) {
            // TODO 「hcs 20240323」 其他类型的异常处理
        } finally {
            this.saveRecord(response);
        }
        // 后置处理
        postHandle();
        return response;
    }

    public AbstractHttpHandler url(String url) {
        this.url = url;
        return this;
    }

    public AbstractHttpHandler host(String host) {
        this.host = host;
        return this;
    }

    public AbstractHttpHandler path(String path) {
        this.path = path;
        return this;
    }

    public AbstractHttpHandler method(HttpMethod httpMethod) {
        this.httpMethod = httpMethod;
        return this;
    }

    public AbstractHttpHandler httpEntity(HttpEntity<?> httpEntity) {
        this.httpEntity = httpEntity;
        return this;
    }

    public AbstractHttpHandler contentType(MediaType contentType) {
        this.headers.setContentType(contentType);
        return this;
    }

    public AbstractHttpHandler bearerAuth(String token) {
        this.headers.setBearerAuth(token);
        return this;
    }

    public AbstractHttpHandler addHeaders(String key, String value) {
        this.headers.add(key, value);
        return this;
    }

    public AbstractHttpHandler params(Object params) {
        this.params = params;
        return this;
    }

    public AbstractHttpHandler addParam(String key, Object value) {
        this.paramMap.put(key, value);
        return this;
    }

    public AbstractHttpHandler token(String token) {
        this.token = token;
        return this;
    }

    // ==================================== protected start ====================================

    /**
     * 获取第三方系统类型
     * 子类必须Override
     *
     * @return ThirdSystem
     */
    abstract protected ThirdSystem getThirdSystem();

    /**
     * 刷新token
     * 子类必须Override
     */
    abstract protected void refreshToken();

    protected JSONObject executeExchange() {
        ResponseEntity<JSONObject> responseEntity = this.restTemplate.exchange(url, httpMethod,
                httpEntity, JSONObject.class, paramMap);

        return responseEntity.getBody();
    }

    protected void preHandle() {

    }

    protected void postHandle() {

    }

    /**
     * 解析响应是否成功，默认取「success」字段
     * 字段非「success」时需要Override
     *
     * @param response 接口响应
     * @return boolean
     */
    protected boolean analysisSuccess(JSONObject response) {
        return response.getBoolean("success");
    }

    /**
     * 解析响应代码，默认取「code」字段
     * 字段非「code」时需要Override
     *
     * @param response 接口响应
     * @return int
     */
    protected int analysisCode(JSONObject response) {
        return response.getInteger("code");
    }

    /**
     * 解析响应消息，默认取「message」字段
     * 字段非「message」时需要Override
     *
     * @param response 接口响应
     * @return String
     */
    protected String analysisMessage(JSONObject response) {
        return response.getString("message");
    }

    // ==================================== protected end ====================================


    // ==================================== private start ====================================

    private void initialize() {
        this.initRestTemplate();
        this.initRecord();
        this.initRequest();
    }

    private void initRecord() {
        record = new ThirdRecord();
        record.setRecordId(UUIDUtil.gen32UUID());
        record.setThirdSystem(nullToDefault(getThirdSystem(), ThirdSystem.UNKNOWN).name());
        record.setRequestTime(LocalDateTime.now());

        recordRequest = new ThirdRecordRequest();
        recordRequest.setRecordId(record.getRecordId());

        recordResponse = new ThirdRecordResponse();
        recordResponse.setRecordId(record.getRecordId());
    }

    private void initRequest() {
        this.initUrl();
        this.initHeaders();
        this.initHttpEntity();
    }

    private void initUrl() {
        if (StringUtil.isBlank(this.url)) {
            this.initHost();
            this.url = this.host + this.path;
        }

        record.setApiUrl(this.url);
        record.setApiPath(this.path);
        String url = this.url;
        // 如果是GET请求，将url中的参数占位符替换成参数值
        if (Objects.equals(httpMethod, HttpMethod.GET)) {
            URI uri = restTemplate.getUriTemplateHandler().expand(url, paramMap);
            url = uri.toString();
        }
        recordRequest.setApiUrl(url);
    }

    private void initHost() {
        if (isNull(apiHostConfig)) {
            apiHostConfig = SpringUtil.getBean(ApiHostConfig.class);
        }
        host = apiHostConfig.getHost(getThirdSystem());
    }

    private void initHeaders() {
        // 如果未设置Content-Type请求头，则默认设置为「application/json」
        if (isNull(headers.getContentType())) {
            this.headers.setContentType(MediaType.APPLICATION_JSON);
        }
    }

    private void initHttpEntity() {
        if (isNull(this.httpEntity)) {
            this.httpEntity = new HttpEntity<>(this.params, this.headers);
        }

        recordRequest.setHttpMethod(this.httpMethod.name());
        recordRequest.setContentType(StringUtil.toString(this.headers.getContentType()));
        recordRequest.setHeaders(JsonUtil.toJson(this.headers));
        recordRequest.setParams(JsonUtil.toJson(this.params));
    }

    private void initRestTemplate() {
        if (isNull(this.restTemplate)) {
            this.restTemplate = (RestTemplate) SpringUtil.getBean("customRestTemplate");
            if (isNull(this.restTemplate)) {
                log.info("============ build new restTemplate");
                this.restTemplate = RestHelper.build().getRestTemplate();
            }
        }
    }

    private void saveRecord(JSONObject response) {
        record.setState(this.analysisSuccess(response) ? 1 : 0);
        record.setResponseTime(LocalDateTime.now());
        record.setUsedTime(Duration.between(record.getRequestTime(), record.getResponseTime()).toMillis());

        recordResponse.setSuccess(record.getState());
        recordResponse.setCode(this.analysisCode(response) + "");
        recordResponse.setMessage(this.analysisMessage(response));
        recordResponse.setResponse(StringUtil.toString(response));

        // 异步保存接口参数、响应和调用记录
        CompletableFuture.runAsync(() -> {
            ThirdRecordService thirdRecordService = SpringUtil.getBean(ThirdRecordService.class);
            ThirdRecordRequestService thirdRecordRequestService = SpringUtil.getBean(ThirdRecordRequestService.class);
            ThirdRecordResponseService thirdRecordResponseService = SpringUtil.getBean(ThirdRecordResponseService.class);

            thirdRecordService.insert(record);
            thirdRecordRequestService.insert(recordRequest);
            thirdRecordResponseService.insert(recordResponse);

        }, ThreadUtil.globalThreadPool());

    }

    // ==================================== private start ====================================
}
