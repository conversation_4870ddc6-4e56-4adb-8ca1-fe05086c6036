package cn.need.cloud.biz;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

import java.net.InetAddress;

@Slf4j
@SpringBootApplication(scanBasePackages = {"cn.need"})
@EnableFeignClients(basePackages = {
        "cn.need.cloud.dict.client.api",
        "cn.need.cloud.upms.client.api",
        "cn.need.cloud.dfs.client.api",
        "cn.need.cloud.ship.client.api"
})
@EnableDiscoveryClient
public class FrpBusinessBootstrap {
    public static void main(String[] args) throws Exception {
        //SpringApplication.run(companyBootstrap.class, args);

        ConfigurableApplicationContext applicationContext = SpringApplication.run(FrpBusinessBootstrap.class, args);
        ConfigurableEnvironment env = applicationContext.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        //noinspection HttpUrlsUsage
        String url = String.format("Swagger UI: http://%s:%s/doc.html", ip, port);
        log.info("********************** FRP业务服务{}{}", url, " biz service startup complete **********************");
    }
}
