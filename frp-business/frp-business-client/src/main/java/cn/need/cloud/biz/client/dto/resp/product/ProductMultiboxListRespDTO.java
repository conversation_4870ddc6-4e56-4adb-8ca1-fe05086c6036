package cn.need.cloud.biz.client.dto.resp.product;

import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * vo对象
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@Schema(description = "产品多箱 结果集vo ")
public class ProductMultiboxListRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * detailList
     */
    @Schema(description = "detailList")
    private List<MultiboxDetailRespDTO> detailList;

    /**
     * 行号
     */
    @Schema(description = "行号")
    private Integer lineNum;

    /**
     * 产品
     */
    @Schema(description = "产品")
    private BaseProductDTO baseProductDTO;

    /**
     * 创建时间
     */
    @Schema(description = "createTime")
    private LocalDateTime createTime;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private Integer multiboxVersionInt;

    /**
     * 发货长度
     */
    @Schema(description = "发货长度")
    private BigDecimal shipLength;

    /**
     * 发货宽度
     */
    @Schema(description = "发货宽度")
    private BigDecimal shipWidth;

    /**
     * 发货高度
     */
    @Schema(description = "发货高度")
    private BigDecimal shipHeight;

    /**
     * 发货重量
     */
    @Schema(description = "发货重量")
    private BigDecimal shipWeight;

    /**
     * 发货重量单位
     */
    @Schema(description = "发货重量单位")
    private String shipWeightUnit;

    /**
     * 发货尺寸单位
     */
    @Schema(description = "发货尺寸单位")
    private String shipDimensionUnit;
}