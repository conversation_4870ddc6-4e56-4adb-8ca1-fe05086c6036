package cn.need.cloud.biz.client.dto.resp.product;

import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 产品多箱 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@Schema(description = " 产品多箱 dto对象")
public class MultiboxDetailRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 产品
     */
    @Schema(description = "产品")
    private BaseProductDTO baseProductDTO;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * 产品标题
     */
    @Schema(description = "产品标题")
    private String title;

    /**
     * 组装产品标志
     */
    @Schema(description = "组装产品标志")
    private Boolean assemblyProductFlag;

    /**
     * 多箱标志
     */
    @Schema(description = "多箱标志")
    private Boolean multiboxFlag;

    /**
     * 组类型
     */
    @Schema(description = "组类型")
    private String groupType;

    /**
     * 行号
     */
    @Schema(description = "行号")
    private Integer lineNum;


    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer qty;

    /**
     * 创建时间
     */
    @Schema(description = "createTime")
    private LocalDateTime createTime;


}