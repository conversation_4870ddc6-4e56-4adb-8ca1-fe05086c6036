package cn.need.cloud.biz.client.dto.otc;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * OTC请求包裹 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OtcRequestPackageDTO extends SuperDTO {


    /**
     * 行序号
     */
    private Integer lineNum;

    /**
     * 快递号
     */
    private String trackingNum;

    /**
     * 是否快递标志
     */
    private Boolean shipExpressFlag;

    /**
     * 运输方式
     */
    private String shipMethod;

    /**
     * 运输公司
     */
    private String shipCarrier;

    /**
     * 运输箱子-长
     */
    private BigDecimal shipSizeLength;

    /**
     * 运输箱子-宽
     */
    private BigDecimal shipSizeWidth;

    /**
     * 运输箱子-高
     */
    private BigDecimal shipSizeHeight;

    /**
     * 运输箱子-重量
     */
    private BigDecimal shipSizeWeight;

    /**
     * 备注
     */
    private String note;

    /**
     * OTC请求ID
     */
    private Long otcRequestId;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 运输箱子-长度单位
     */
    private String shipSizeDimensionUnit;

    /**
     * 运输箱子-重量单位
     */
    private String shipSizeWeightUnit;

    /**
     * 多箱UPC
     */
    private String packageMultiboxUpc;

    /**
     * 多箱行号
     */
    private Integer packageMultiboxLineNum;

    /**
     * 多箱产品ID
     */
    private Long packageMultiboxProductId;

    /**
     * 多箱版本号
     */
    private Integer packageMultiboxVersionInt;

}