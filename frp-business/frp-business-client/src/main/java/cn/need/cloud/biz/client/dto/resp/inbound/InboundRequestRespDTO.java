package cn.need.cloud.biz.client.dto.resp.inbound;

import cn.need.cloud.biz.client.dto.req.base.BaseLogisticPartnerAndWarehouseReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 入库请求 dto对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "入库请求 dto对象")
public class InboundRequestRespDTO extends BaseLogisticPartnerAndWarehouseReqDTO {


    @Serial
    private static final long serialVersionUID = -7807680698369691452L;
    /**
     * 运输方式类型
     */
    @Schema(description = "运输方式类型")
    private String transportMethodType;

    /**
     * 外部唯一标识码
     */
    @Schema(description = "外部唯一标识码")
    private String requestRefNum;

    /**
     * 预计到达日期
     */
    @Schema(description = "预计到达日期")
    private LocalDateTime estimateArrivalDate;

    /**
     * 实际到达日期
     */
    @Schema(description = "实际到达日期")
    private LocalDateTime actualArrivalDate;

    /**
     * 快递号
     */
    @Schema(description = "快递号")
    private String trackingNum;

    /**
     * 发件人姓名
     */
    @Schema(description = "发件人姓名")
    private String fromAddressName;

    /**
     * 发件人公司
     */
    @Schema(description = "发件人公司")
    private String fromAddressCompany;

    /**
     * 发件人国家
     */
    @Schema(description = "发件人国家")
    private String fromAddressCountry;

    /**
     * 发件人州
     */
    @Schema(description = "发件人州")
    private String fromAddressState;

    /**
     * 发件人城市
     */
    @Schema(description = "发件人城市")
    private String fromAddressCity;

    /**
     * 发件人邮编
     */
    @Schema(description = "发件人邮编")
    private String fromAddressZipCode;

    /**
     * 发件人地址1
     */
    @Schema(description = "发件人地址1")
    private String fromAddressAddr1;

    /**
     * 发件人地址2
     */
    @Schema(description = "发件人地址2")
    private String fromAddressAddr2;

    /**
     * 发件人地址3
     */
    @Schema(description = "发件人地址3")
    private String fromAddressAddr3;

    /**
     * 发件人邮箱
     */
    @Schema(description = "发件人邮箱")
    private String fromAddressEmail;

    /**
     * 发件人电话
     */
    @Schema(description = "发件人电话")
    private String fromAddressPhone;

    /**
     * 发件人备注
     */
    @Schema(description = "发件人备注")
    private String fromAddressNote;

    // /**
    //  * 交易伙伴
    //  */
    // @Schema(description = "交易伙伴")
    // private BasePartnerDTO transactionPartner;

    /**
     * 请求状态
     */
    @Schema(description = "请求状态")
    private String inboundRequestStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 容器类型
     */
    @Schema(description = "容器类型")
    private String containerType;

    /**
     * 发件人地址是否为住宅
     */
    @Schema(description = "发件人地址是否为住宅")
    private Boolean fromAddressIsResidential;

    /**
     * 入库请求单详情
     */
    @Schema(description = "入库请求单详情")
    private List<InboundRequestDetailRespDTO> details;

    /**
     * 仓库基本信息
     */
    @Schema(description = "仓库基本信息")
    private BaseWarehouseDTO baseWarehouseDTO;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;

    /**
     * 处理开始时间
     */
    @Schema(description = "处理开始时间")
    private LocalDateTime processStartTime;

    /**
     * 处理完成时间
     */
    @Schema(description = "处理完成时间")
    private LocalDateTime processEndTime;

    /**
     * 计费状态
     */
    @Schema(description = "计费状态")
    private String feeStatus;
}