package cn.need.cloud.biz.client.dto.req.base;

import cn.need.cloud.biz.client.annotation.NotEmptyOfAllField;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * 产品对象
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@Schema(description = "产品对象")
public class BaseProductReqDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -9050326514403789806L;

    @NotNull(message = "product can not null")
    @Schema(description = "产品信息")
    @NotEmptyOfAllField(message = "refNum and supplierSku cannot both be null.", columns = {"refNum", "supplierSku"})
    private ProductReqDTO product;

    @JsonIgnore
    public Long getProductId() {
        if (product != null) {
            return product.getProductId();
        }
        return null;
    }
}
