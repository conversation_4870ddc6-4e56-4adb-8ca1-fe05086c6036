/*
 Navicat Premium Dump SQL

 Source Server         : Frp-ipower-dev
 Source Server Type    : MySQL
 Source Server Version : 80037 (8.0.37)
 Source Host           : ***************:30201
 Source Schema         : frp_business_dev

 Target Server Type    : MySQL
 Target Server Version : 80037 (8.0.37)
 File Encoding         : 65001

 Date: 02/04/2025 18:24:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for bin_location
-- ----------------------------
DROP TABLE IF EXISTS `bin_location`;
CREATE TABLE `bin_location` (
  `id` bigint NOT NULL COMMENT '主键',
  `location_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '库位名称',
  `lrow` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '行',
  `ldepth` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '列',
  `llevel` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '层',
  `lsplit` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '格',
  `note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `active_flag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效 1：有效，0：无效',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `bin_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '库位类型（用户可修改）',
  `bin_product_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '库位上产品的一个状态  Empty   Pallet  散货  ',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '库位类型（用户不可修改）',
  `warehouse_zone_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '仓库分区',
  `default_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否默认',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` datetime NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_bin_location_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_bin_location_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_bin_location_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_location_name` (`location_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='库位';

-- ----------------------------
-- Table structure for bin_location_detail
-- ----------------------------
DROP TABLE IF EXISTS `bin_location_detail`;
CREATE TABLE `bin_location_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `in_stock_qty` int NOT NULL COMMENT '库存数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_bin_location_detail_id` (`id`) USING BTREE,
  KEY `ix_bin_location_detail_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_bin_location_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_bin_location_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_bin_location_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_bin_location_detail_header_id` (`bin_location_id`) USING BTREE,
  CONSTRAINT `bin_location_detail_chk_1` CHECK ((`in_stock_qty` >= 0))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='库位详情';

-- ----------------------------
-- Table structure for bin_location_detail_locked
-- ----------------------------
DROP TABLE IF EXISTS `bin_location_detail_locked`;
CREATE TABLE `bin_location_detail_locked` (
  `id` bigint NOT NULL COMMENT '主键',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `qty` int NOT NULL COMMENT '数量',
  `finish_qty` int NOT NULL COMMENT '完成数量',
  `locked_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '锁定状态',
  `ref_table_id` bigint NOT NULL COMMENT '关联表Id',
  `ref_table_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `ref_table_ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '关联表唯一码',
  `ref_table_show_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '展示关联名称',
  `ref_table_show_ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '展示关联RefNum',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `product_version_id` bigint NOT NULL COMMENT '版本产品id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_locked_bin_location_detail_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_locked_bin_location_detail_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_locked_bin_location_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_locked_bin_location_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_locked_bin_location_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='锁定 库位详情';

-- ----------------------------
-- Table structure for bin_location_reserve
-- ----------------------------
DROP TABLE IF EXISTS `bin_location_reserve`;
CREATE TABLE `bin_location_reserve` (
  `id` bigint NOT NULL COMMENT '主键',
  `qty` int NOT NULL COMMENT '数量',
  `finish_qty` int NOT NULL,
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_bin_location_reserve_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_bin_location_reserve_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_bin_location_reserve_product_id` (`product_id`) USING BTREE,
  KEY `ix_bin_location_reserve_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_bin_location_reserve_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='预留库位';

-- ----------------------------
-- Table structure for fee_config_inbound
-- ----------------------------
DROP TABLE IF EXISTS `fee_config_inbound`;
CREATE TABLE `fee_config_inbound` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `active_flag` tinyint(1) NOT NULL COMMENT '是否有效(0-无效，1-有效)',
  `condition_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '计费条件类型（决定区间判断的依据）',
  `currency` varchar(3) COLLATE utf8mb4_bin NOT NULL COMMENT '货币代码（如USD、CNY）',
  `fee_calculation_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '费用计费类型(detail得出的结果，是否要乘condition_type得出的值)',
  `extra_fee_judge_fields` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '额外计费判断字段',
  `name` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `quote_id` bigint DEFAULT NULL COMMENT '仓库报价id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_fee_config_inbound_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_config_quote_id` (`quote_id`) COMMENT '仓库报价id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库报价费用配置inbound';

-- ----------------------------
-- Table structure for fee_config_inbound_detail
-- ----------------------------
DROP TABLE IF EXISTS `fee_config_inbound_detail`;
CREATE TABLE `fee_config_inbound_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `fee_unit_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '计费单位类型',
  `line_num` int NOT NULL COMMENT '行序号',
  `base_fee` decimal(18,3) NOT NULL COMMENT '基础价格',
  `unit_fee` decimal(18,3) NOT NULL COMMENT '单价',
  `fee_start_threshold` decimal(18,3) NOT NULL COMMENT '计费起始阈值（实际数量超过该值时触发计费）',
  `section_start` bigint NOT NULL COMMENT '开始（包含）根据condition_type得到值来判断',
  `section_end` bigint NOT NULL COMMENT '结束（不包含）根据condition_type得到值来判断',
  `header_id` bigint NOT NULL COMMENT 'header表id',
  PRIMARY KEY (`id`),
  KEY `ix_fee_config_inbound_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_config_header_id` (`header_id`) COMMENT 'header_id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库报价费用配置inbound详情';

-- ----------------------------
-- Table structure for fee_config_otb
-- ----------------------------
DROP TABLE IF EXISTS `fee_config_otb`;
CREATE TABLE `fee_config_otb` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `active_flag` tinyint(1) NOT NULL COMMENT '是否有效(0-无效，1-有效)',
  `condition_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '计费条件类型（决定区间判断的依据）',
  `currency` varchar(3) COLLATE utf8mb4_bin NOT NULL COMMENT '货币代码（如USD、CNY）',
  `fee_calculation_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '费用计费类型(detail得出的结果，是否要乘condition_type得出的值)',
  `extra_fee_judge_fields` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '额外计费判断字段',
  `name` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `quote_id` bigint DEFAULT NULL COMMENT '仓库报价id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_fee_config_otb_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_config_quote_id` (`quote_id`) COMMENT '仓库报价id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库报价费用配置otb';

-- ----------------------------
-- Table structure for fee_config_otb_detail
-- ----------------------------
DROP TABLE IF EXISTS `fee_config_otb_detail`;
CREATE TABLE `fee_config_otb_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `fee_unit_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '计费单位类型',
  `line_num` int NOT NULL COMMENT '行序号',
  `base_fee` decimal(18,3) NOT NULL COMMENT '基础价格',
  `unit_fee` decimal(18,3) NOT NULL COMMENT '单价',
  `fee_start_threshold` decimal(18,3) NOT NULL COMMENT '计费起始阈值（实际数量超过该值时触发计费）',
  `section_start` bigint NOT NULL COMMENT '开始（包含）根据condition_type得到值来判断',
  `section_end` bigint NOT NULL COMMENT '结束（不包含）根据condition_type得到值来判断',
  `header_id` bigint NOT NULL COMMENT 'header表id',
  PRIMARY KEY (`id`),
  KEY `ix_fee_config_otb_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_config_header_id` (`header_id`) COMMENT 'header_id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库报价费用配置otb详情';

-- ----------------------------
-- Table structure for fee_config_otc
-- ----------------------------
DROP TABLE IF EXISTS `fee_config_otc`;
CREATE TABLE `fee_config_otc` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `active_flag` tinyint(1) NOT NULL COMMENT '是否有效(0-无效，1-有效)',
  `condition_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '计费条件类型（决定区间判断的依据）',
  `currency` varchar(3) COLLATE utf8mb4_bin NOT NULL COMMENT '货币代码（如USD、CNY）',
  `fee_calculation_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '费用计费类型(detail得出的结果，是否要乘condition_type得出的值)',
  `extra_fee_judge_fields` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '额外计费判断字段',
  `name` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `quote_id` bigint DEFAULT NULL COMMENT '仓库报价id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_fee_config_otc_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_config_quote_id` (`quote_id`) COMMENT '仓库报价id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库报价费用配置otc';

-- ----------------------------
-- Table structure for fee_config_otc_detail
-- ----------------------------
DROP TABLE IF EXISTS `fee_config_otc_detail`;
CREATE TABLE `fee_config_otc_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `fee_unit_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '计费单位类型',
  `line_num` int NOT NULL COMMENT '行序号',
  `base_fee` decimal(18,3) NOT NULL COMMENT '基础价格',
  `unit_fee` decimal(18,3) NOT NULL COMMENT '单价',
  `fee_start_threshold` decimal(18,3) NOT NULL COMMENT '计费起始阈值（实际数量超过该值时触发计费）',
  `section_start` bigint NOT NULL COMMENT '开始（包含）根据condition_type得到值来判断',
  `section_end` bigint NOT NULL COMMENT '结束（不包含）根据condition_type得到值来判断',
  `header_id` bigint NOT NULL COMMENT 'header表id',
  PRIMARY KEY (`id`),
  KEY `ix_fee_config_otc_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_config_header_id` (`header_id`) COMMENT 'header_id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库报价费用配置otc详情';

-- ----------------------------
-- Table structure for fee_config_storage
-- ----------------------------
DROP TABLE IF EXISTS `fee_config_storage`;
CREATE TABLE `fee_config_storage` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `active_flag` tinyint(1) NOT NULL COMMENT '是否有效(0-无效，1-有效)',
  `condition_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '计费条件类型（决定区间判断的依据）',
  `currency` varchar(3) COLLATE utf8mb4_bin NOT NULL COMMENT '货币代码（如USD、CNY）',
  `fee_calculation_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '费用计费类型(detail得出的结果，是否要乘condition_type得出的值)',
  `extra_fee_judge_fields` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '额外计费判断字段',
  `name` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `quote_id` bigint DEFAULT NULL COMMENT '仓库报价id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_fee_config_storage_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_config_quote_id` (`quote_id`) COMMENT '仓库报价id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库报价费用配置storage';

-- ----------------------------
-- Table structure for fee_config_storage_detail
-- ----------------------------
DROP TABLE IF EXISTS `fee_config_storage_detail`;
CREATE TABLE `fee_config_storage_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `fee_unit_type` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '计费单位类型',
  `line_num` int NOT NULL COMMENT '行序号',
  `base_fee` decimal(18,3) NOT NULL COMMENT '基础价格',
  `unit_fee` decimal(18,3) NOT NULL COMMENT '单价',
  `fee_start_threshold` decimal(18,3) NOT NULL COMMENT '计费起始阈值（实际数量超过该值时触发计费）',
  `section_start` bigint NOT NULL COMMENT '开始（包含）根据condition_type得到值来判断',
  `section_end` bigint NOT NULL COMMENT '结束（不包含）根据condition_type得到值来判断',
  `header_id` bigint NOT NULL COMMENT 'header表id',
  PRIMARY KEY (`id`),
  KEY `ix_fee_config_storage_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_config_header_id` (`header_id`) COMMENT 'header_id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库报价费用配置storage详情';

-- ----------------------------
-- Table structure for fee_inbound
-- ----------------------------
DROP TABLE IF EXISTS `fee_inbound`;
CREATE TABLE `fee_inbound` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴id',
  `fee_original_data_id` bigint NOT NULL COMMENT '费用原始数据表id',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `currency` varchar(3) COLLATE utf8mb4_bin NOT NULL COMMENT '货币代码（如USD、CNY）',
  `total_fee` decimal(18,3) NOT NULL COMMENT '总费用',
  `snapshot_request_id` bigint NOT NULL COMMENT '请求id',
  `snapshot_ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `snapshot_request_ref_num` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '外部唯一标识码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_fee_inbound_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_inbound_snapshot_request_ref_num` (`snapshot_request_ref_num`) COMMENT 'snapshot_request_ref_num索引',
  KEY `ix_fee_inbound_snapshot_ref_num` (`snapshot_ref_num`) COMMENT 'snapshot_ref_num索引',
  KEY `ix_fee_inbound_ref_num` (`ref_num`) COMMENT 'ref_num索引',
  KEY `ix_fee_inbound_snapshot_request_id` (`snapshot_request_id`) COMMENT 'snapshot_request_id索引',
  KEY `ix_fee_inbound_transaction_partner_id` (`transaction_partner_id`) COMMENT 'transaction_partner_id索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='费用inbound';

-- ----------------------------
-- Table structure for fee_inbound_detail
-- ----------------------------
DROP TABLE IF EXISTS `fee_inbound_detail`;
CREATE TABLE `fee_inbound_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `fee_config_id` bigint NOT NULL COMMENT '费用配置id',
  `fee_config_detail_id` bigint NOT NULL COMMENT '费用配置明细id',
  `line_num` int NOT NULL COMMENT '行序号',
  `fee` decimal(18,3) NOT NULL COMMENT '费用',
  `header_id` bigint NOT NULL COMMENT 'header表id',
  PRIMARY KEY (`id`),
  KEY `ix_fee_inbound_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_inbound_detail_header_id` (`header_id`) COMMENT 'header表id索引',
  KEY `ix_fee_inbound_detail_fee_config_id` (`fee_config_id`) COMMENT '费用配置id索引',
  KEY `ix_fee_inbound_detail_fee_config_detail_id` (`fee_config_detail_id`) COMMENT '费用配置明细id索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='费用详情inbound';

-- ----------------------------
-- Table structure for fee_original_data
-- ----------------------------
DROP TABLE IF EXISTS `fee_original_data`;
CREATE TABLE `fee_original_data` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴id',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `process_end_time` timestamp NOT NULL COMMENT '处理完成时间',
  `process_start_time` timestamp NOT NULL COMMENT '开始处理时间',
  `snapshot_request_id` bigint NOT NULL COMMENT '请求id',
  `snapshot_ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `snapshot_request_ref_num` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '外部唯一标识码',
  `extra_data` text COLLATE utf8mb4_bin NOT NULL COMMENT '额外数据(Json)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_fee_original_data_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_original_data_ref_num` (`ref_num`) COMMENT 'ref_num索引',
  KEY `ix_fee_original_data_snapshot_request_ref_num` (`snapshot_request_ref_num`) COMMENT 'snapshot_request_ref_num索引',
  KEY `ix_fee_original_data_snapshot_ref_num` (`snapshot_ref_num`) COMMENT 'snapshot_ref_num索引',
  KEY `ix_fee_original_data_snapshot_request_id` (`snapshot_request_id`) COMMENT 'snapshot_request_id索引',
  KEY `ix_fee_original_data_transaction_partner_id` (`transaction_partner_id`) COMMENT 'transaction_partner_id索引',
  KEY `ix_fee_original_data_start_time` (`process_end_time`) COMMENT 'start_time索引',
  KEY `ix_fee_original_data_end_time` (`process_start_time`) COMMENT 'end_time索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='费用原始数据表';

-- ----------------------------
-- Table structure for fee_otb
-- ----------------------------
DROP TABLE IF EXISTS `fee_otb`;
CREATE TABLE `fee_otb` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴id',
  `fee_original_data_id` bigint NOT NULL COMMENT '费用原始数据表id',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `currency` varchar(3) COLLATE utf8mb4_bin NOT NULL COMMENT '货币代码（如USD、CNY）',
  `total_fee` decimal(18,3) NOT NULL COMMENT '总费用',
  `snapshot_request_id` bigint NOT NULL COMMENT '请求id',
  `snapshot_ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `snapshot_request_ref_num` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '外部唯一标识码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_fee_otb_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_otb_snapshot_request_ref_num` (`snapshot_request_ref_num`) COMMENT 'snapshot_request_ref_num索引',
  KEY `ix_fee_otb_snapshot_ref_num` (`snapshot_ref_num`) COMMENT 'snapshot_ref_num索引',
  KEY `ix_fee_otb_ref_num` (`ref_num`) COMMENT 'ref_num索引',
  KEY `ix_fee_otb_snapshot_request_id` (`snapshot_request_id`) COMMENT 'snapshot_request_id索引',
  KEY `ix_fee_otb_transaction_partner_id` (`transaction_partner_id`) COMMENT 'transaction_partner_id索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='费用otb';

-- ----------------------------
-- Table structure for fee_otb_detail
-- ----------------------------
DROP TABLE IF EXISTS `fee_otb_detail`;
CREATE TABLE `fee_otb_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `fee_config_id` bigint NOT NULL COMMENT '费用配置id',
  `fee_config_detail_id` bigint NOT NULL COMMENT '费用配置明细id',
  `line_num` int NOT NULL COMMENT '行序号',
  `fee` decimal(18,3) NOT NULL COMMENT '费用',
  `header_id` bigint NOT NULL COMMENT 'header表id',
  PRIMARY KEY (`id`),
  KEY `ix_fee_otb_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_otb_detail_header_id` (`header_id`) COMMENT 'header表id索引',
  KEY `ix_fee_otb_detail_fee_config_id` (`fee_config_id`) COMMENT '费用配置id索引',
  KEY `ix_fee_otb_detail_fee_config_detail_id` (`fee_config_detail_id`) COMMENT '费用配置明细id索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='费用详情otb';

-- ----------------------------
-- Table structure for fee_otc
-- ----------------------------
DROP TABLE IF EXISTS `fee_otc`;
CREATE TABLE `fee_otc` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴id',
  `fee_original_data_id` bigint NOT NULL COMMENT '费用原始数据表id',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `currency` varchar(3) COLLATE utf8mb4_bin NOT NULL COMMENT '货币代码（如USD、CNY）',
  `total_fee` decimal(18,3) NOT NULL COMMENT '总费用',
  `snapshot_request_id` bigint NOT NULL COMMENT '请求id',
  `snapshot_ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `snapshot_request_ref_num` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '外部唯一标识码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_fee_otc_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_otc_snapshot_request_ref_num` (`snapshot_request_ref_num`) COMMENT 'snapshot_request_ref_num索引',
  KEY `ix_fee_otc_snapshot_ref_num` (`snapshot_ref_num`) COMMENT 'snapshot_ref_num索引',
  KEY `ix_fee_otc_ref_num` (`ref_num`) COMMENT 'ref_num索引',
  KEY `ix_fee_otc_snapshot_request_id` (`snapshot_request_id`) COMMENT 'snapshot_request_id索引',
  KEY `ix_fee_otc_transaction_partner_id` (`transaction_partner_id`) COMMENT 'transaction_partner_id索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='费用otc';

-- ----------------------------
-- Table structure for fee_otc_detail
-- ----------------------------
DROP TABLE IF EXISTS `fee_otc_detail`;
CREATE TABLE `fee_otc_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `fee_config_id` bigint NOT NULL COMMENT '费用配置id',
  `fee_config_detail_id` bigint NOT NULL COMMENT '费用配置明细id',
  `line_num` int NOT NULL COMMENT '行序号',
  `fee` decimal(18,3) NOT NULL COMMENT '费用',
  `header_id` bigint NOT NULL COMMENT 'header表id',
  PRIMARY KEY (`id`),
  KEY `ix_fee_otc_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_otc_detail_header_id` (`header_id`) COMMENT 'header表id索引',
  KEY `ix_fee_otc_detail_fee_config_id` (`fee_config_id`) COMMENT '费用配置id索引',
  KEY `ix_fee_otc_detail_fee_config_detail_id` (`fee_config_detail_id`) COMMENT '费用配置明细id索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='费用详情otc';

-- ----------------------------
-- Table structure for fee_storage
-- ----------------------------
DROP TABLE IF EXISTS `fee_storage`;
CREATE TABLE `fee_storage` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴id',
  `fee_original_data_id` bigint NOT NULL COMMENT '费用原始数据表id',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `currency` varchar(3) COLLATE utf8mb4_bin NOT NULL COMMENT '货币代码（如USD、CNY）',
  `total_fee` decimal(18,3) NOT NULL COMMENT '总费用',
  `snapshot_request_id` bigint NOT NULL COMMENT '请求id',
  `snapshot_ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `snapshot_request_ref_num` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '外部唯一标识码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_fee_storage_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_storage_snapshot_request_ref_num` (`snapshot_request_ref_num`) COMMENT 'snapshot_request_ref_num索引',
  KEY `ix_fee_storage_snapshot_ref_num` (`snapshot_ref_num`) COMMENT 'snapshot_ref_num索引',
  KEY `ix_fee_storage_ref_num` (`ref_num`) COMMENT 'ref_num索引',
  KEY `ix_fee_storage_snapshot_request_id` (`snapshot_request_id`) COMMENT 'snapshot_request_id索引',
  KEY `ix_fee_storage_transaction_partner_id` (`transaction_partner_id`) COMMENT 'transaction_partner_id索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='费用storage';

-- ----------------------------
-- Table structure for fee_storage_detail
-- ----------------------------
DROP TABLE IF EXISTS `fee_storage_detail`;
CREATE TABLE `fee_storage_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `fee_config_id` bigint NOT NULL COMMENT '费用配置id',
  `fee_config_detail_id` bigint NOT NULL COMMENT '费用配置明细id',
  `line_num` int NOT NULL COMMENT '行序号',
  `fee` decimal(18,3) NOT NULL COMMENT '费用',
  `header_id` bigint NOT NULL COMMENT 'header表id',
  PRIMARY KEY (`id`),
  KEY `ix_fee_storage_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_fee_storage_detail_header_id` (`header_id`) COMMENT 'header表id索引',
  KEY `ix_fee_storage_detail_fee_config_id` (`fee_config_id`) COMMENT '费用配置id索引',
  KEY `ix_fee_storage_detail_fee_config_detail_id` (`fee_config_detail_id`) COMMENT '费用配置明细id索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='费用详情storage';

-- ----------------------------
-- Table structure for inbound_pallet
-- ----------------------------
DROP TABLE IF EXISTS `inbound_pallet`;
CREATE TABLE `inbound_pallet` (
  `id` bigint NOT NULL COMMENT '主键',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `pallet_size_length` decimal(18,3) DEFAULT NULL COMMENT '托盘-长',
  `pallet_size_width` decimal(18,3) DEFAULT NULL COMMENT '托盘-宽',
  `pallet_size_height` decimal(18,3) DEFAULT NULL COMMENT '托盘-高',
  `pallet_size_weight` decimal(18,3) DEFAULT NULL COMMENT '托盘-重量',
  `pallet_size_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '托盘-重量单位',
  `pallet_size_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '托盘-长度单位',
  `pallet_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打托状态',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `bin_location_id` bigint DEFAULT NULL COMMENT '库位id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `carton_per_layer` int NOT NULL COMMENT '每层几个箱子',
  `layers_count` int NOT NULL COMMENT '一共多少层',
  `ext_carton` int NOT NULL COMMENT '多了几个箱子',
  `pcs_per_carton` int NOT NULL COMMENT '每箱几个产品',
  `pallet_empty_profile_id` bigint DEFAULT NULL COMMENT '空托盘id',
  `pallet_template_id` bigint DEFAULT NULL COMMENT '托盘模板id',
  `product_id` bigint DEFAULT NULL COMMENT '产品id',
  `product_version_id` bigint DEFAULT NULL COMMENT '版本产品id',
  `inbound_workorder_id` bigint DEFAULT NULL COMMENT '入库工单id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_pallet_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_pallet_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_pallet_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_pallet_bin_location_id` (`bin_location_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入库单打托';

-- ----------------------------
-- Table structure for inbound_pallet_detail
-- ----------------------------
DROP TABLE IF EXISTS `inbound_pallet_detail`;
CREATE TABLE `inbound_pallet_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `carton_per_layer` int NOT NULL COMMENT '每层几个箱子',
  `layers_count` int NOT NULL COMMENT '一共多少层',
  `ext_carton` int NOT NULL COMMENT '多了几个箱子',
  `pcs_per_carton` int NOT NULL COMMENT '每箱几个产品',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `inbound_workorder_id` bigint NOT NULL COMMENT '入库工单id',
  `inbound_unload_id` bigint NOT NULL COMMENT '卸货id',
  `putaway_slip_id` bigint NOT NULL COMMENT '上架id',
  `putaway_slip_detail_id` bigint DEFAULT NULL COMMENT '上架详情id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `inbound_pallet_id` bigint NOT NULL COMMENT '打托id',
  `bin_location_detail_id` bigint DEFAULT NULL COMMENT '库位详情id',
  `bin_location_id` bigint DEFAULT NULL COMMENT '库位id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `qty` bigint DEFAULT NULL COMMENT '打托卸货数量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_pallet_detail_inbound_pallet_id` (`inbound_pallet_id`) USING BTREE,
  KEY `ix_pallet_detail_inbound_unload_id` (`inbound_unload_id`) USING BTREE,
  KEY `ix_pallet_detail_inbound_workorder_id` (`inbound_workorder_id`) USING BTREE,
  KEY `ix_pallet_detail_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_pallet_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_pallet_detail_putaway_slip_detail_id` (`putaway_slip_detail_id`) USING BTREE,
  KEY `ix_pallet_detail_putaway_slip_id` (`putaway_slip_id`) USING BTREE,
  KEY `ix_pallet_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_pallet_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_pallet_detail_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_pallet_detail_bin_location_id` (`bin_location_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入库单打托详情';

-- ----------------------------
-- Table structure for inbound_putaway_slip
-- ----------------------------
DROP TABLE IF EXISTS `inbound_putaway_slip`;
CREATE TABLE `inbound_putaway_slip` (
  `id` bigint NOT NULL COMMENT '主键',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `inbound_putaway_slip_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '上架状态',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_putaway_slip_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_putaway_slip_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_putaway_slip_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='上架';

-- ----------------------------
-- Table structure for inbound_putaway_slip_detail
-- ----------------------------
DROP TABLE IF EXISTS `inbound_putaway_slip_detail`;
CREATE TABLE `inbound_putaway_slip_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `inbound_workorder_detail_id` bigint NOT NULL,
  `inbound_workorder_id` bigint NOT NULL,
  `inbound_unload_id` bigint NOT NULL,
  `inbound_putaway_slip_id` bigint NOT NULL,
  `pick_book_qty` int NOT NULL DEFAULT '0',
  `pick_finish_qty` int NOT NULL DEFAULT '0',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_putaway_slip_detail_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_putaway_slip_detail_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_putaway_slip_detail_inbound_unload_id` (`inbound_unload_id`) USING BTREE,
  KEY `ix_putaway_slip_detail_inbound_workorder_detail_id` (`inbound_workorder_detail_id`) USING BTREE,
  KEY `ix_putaway_slip_detail_inbound_workorder_id` (`inbound_workorder_id`) USING BTREE,
  KEY `ix_putaway_slip_detail_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_putaway_slip_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_putaway_slip_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_putaway_slip_detail_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_putaway_slip_detail_header_id` (`inbound_putaway_slip_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='上架详情';

-- ----------------------------
-- Table structure for inbound_request
-- ----------------------------
DROP TABLE IF EXISTS `inbound_request`;
CREATE TABLE `inbound_request` (
  `id` bigint NOT NULL COMMENT '主键',
  `transport_method_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '运输方式类型',
  `request_ref_num` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '外部唯一标识码',
  `estimate_arrival_date` timestamp NOT NULL COMMENT '预计到达日期',
  `actual_arrival_date` timestamp NULL DEFAULT NULL COMMENT '实际到达日期',
  `tracking_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递号',
  `from_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发件人姓名',
  `from_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人公司',
  `from_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件人国家',
  `from_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件人州',
  `from_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件人城市',
  `from_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人邮编',
  `from_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件人地址1',
  `from_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人地址2',
  `from_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人地址3',
  `from_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人邮箱',
  `from_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人电话',
  `from_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴ID',
  `inbound_request_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'New' COMMENT '请求状态',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `container_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '容器类型',
  `from_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '发件人地址是否为住宅',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) USING BTREE COMMENT '参考编号唯一索引',
  KEY `ix_inbound_request_warehouse_id` (`warehouse_id`) USING BTREE COMMENT '仓库ID索引',
  KEY `ix_inbound_request_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE COMMENT '租户ID和仓库ID组合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入库请求';

-- ----------------------------
-- Table structure for inbound_request_detail
-- ----------------------------
DROP TABLE IF EXISTS `inbound_request_detail`;
CREATE TABLE `inbound_request_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `qty` int NOT NULL COMMENT '数量',
  `inbound_request_id` bigint NOT NULL COMMENT '入库请求Id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `detail_request_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '详情请求参考编号',
  `detail_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '详情类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_inbound_request_detail_header_id` (`inbound_request_id`) USING BTREE,
  KEY `ix_inbound_request_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_inbound_request_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_inbound_request_detail_product_version_id` (`product_version_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入库请求详情';

-- ----------------------------
-- Table structure for inbound_unload
-- ----------------------------
DROP TABLE IF EXISTS `inbound_unload`;
CREATE TABLE `inbound_unload` (
  `id` bigint NOT NULL COMMENT '主键',
  `inbound_workorder_id` bigint NOT NULL COMMENT '入库工单',
  `inbound_workorder_detail_id` bigint NOT NULL COMMENT '入库工单详情',
  `putaway_slip_id` bigint NOT NULL COMMENT '上架单',
  `qty` int NOT NULL COMMENT '数量',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `pallet_qty` int NOT NULL DEFAULT '0',
  `pallet_putaway_qty` int NOT NULL DEFAULT '0',
  `regular_putaway_qty` int NOT NULL DEFAULT '0',
  `inbound_unload_type` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卸货类型',
  `inbound_unload_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '卸货状态',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `request_id` bigint NOT NULL COMMENT '入库请求',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_inbound_unload_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_inbound_unload_inbound_workorder_detail_id` (`inbound_workorder_detail_id`) USING BTREE,
  KEY `ix_inbound_unload_inbound_workorder_id` (`inbound_workorder_id`) USING BTREE,
  KEY `ix_inbound_unload_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_inbound_unload_product_id` (`product_id`) USING BTREE,
  KEY `ix_inbound_unload_putaway_slip_id` (`putaway_slip_id`) USING BTREE,
  KEY `ix_inbound_unload_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_inbound_unload_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_inbound_unload_request_id` (`request_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入库工单卸货表 根据这个来生成上架单';

-- ----------------------------
-- Table structure for inbound_workorder
-- ----------------------------
DROP TABLE IF EXISTS `inbound_workorder`;
CREATE TABLE `inbound_workorder` (
  `id` bigint NOT NULL COMMENT '主键',
  `request_snapshot_transport_method_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '入库请求运输方式类型',
  `request_snapshot_transaction_partner_id` bigint NOT NULL COMMENT 'request快照transctionParterId',
  `inbound_request_id` bigint NOT NULL COMMENT '入库请求id',
  `request_snapshot_request_ref_num` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求快照RequestRefnum',
  `request_snapshot_estimate_arrival_date` timestamp NOT NULL COMMENT '请求单预计到达时间',
  `request_snapshot_actual_arrival_date` timestamp NULL DEFAULT NULL COMMENT '请求单实际到达时间',
  `request_snapshot_tracking_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求单物流跟踪编码',
  `request_snapshot_from_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发件人姓名',
  `request_snapshot_from_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人公司',
  `request_snapshot_from_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件人国家',
  `request_snapshot_from_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件人州',
  `request_snapshot_from_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件人城市',
  `request_snapshot_from_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人邮编',
  `request_snapshot_from_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件人地址1',
  `request_snapshot_from_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人地址2',
  `request_snapshot_from_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人地址3',
  `request_snapshot_from_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人邮箱',
  `request_snapshot_from_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人电话',
  `request_snapshot_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'request快照备注',
  `inbound_workorder_unload_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'New' COMMENT '入库工单 卸货状态',
  `inbound_workorder_putaway_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '入库工单 上架状态',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'None' COMMENT '打印状态',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `request_snapshot_from_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件人备注',
  `request_snapshot_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'request快照RefNum',
  `inbound_workorder_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '入库工单状态',
  `request_snapshot_container_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '容器类型',
  `request_snapshot_from_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '发件人地址是否为住宅',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_inbound_workorder_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_inbound_workorder_inbound_request_id` (`inbound_request_id`) USING BTREE,
  KEY `ix_inbound_workorder_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_inbound_workorder_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入库工单';

-- ----------------------------
-- Table structure for inbound_workorder_detail
-- ----------------------------
DROP TABLE IF EXISTS `inbound_workorder_detail`;
CREATE TABLE `inbound_workorder_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `inbound_request_detail_id` bigint NOT NULL COMMENT '入库请求详情',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `inbound_workorder_id` bigint NOT NULL COMMENT '入库工单id',
  `line_num` int NOT NULL COMMENT '行序号',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `qty` int NOT NULL COMMENT '数量',
  `finish_qty` int NOT NULL DEFAULT '0',
  `request_detail_snapshot_qty` int NOT NULL,
  `request_detail_snapshot_line_num` int NOT NULL DEFAULT '0' COMMENT '请求详情序号',
  `request_detail_snapshot_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求详情备注',
  `request_detail_snapshot_product_version_id` bigint NOT NULL COMMENT '请求详情产品id',
  `need_receive_qty` int NOT NULL,
  `remeasure_flag` tinyint(1) NOT NULL COMMENT '是否重新测量过 默认为false,没有重新测量过 如果发现Product和实际不符，需要走特殊流程更改为对的',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `request_detail_snapshot_detail_request_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求详情请求参考编码',
  `request_detail_snapshot_detail_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求详情类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_inbound_workorder_detail_inbound_request_detail_id` (`inbound_request_detail_id`) USING BTREE,
  KEY `ix_inbound_workorder_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_inbound_workorder_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_inbound_workorder_detail_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_inbound_workorder_detail_header_id` (`inbound_workorder_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入库工单详情';

-- ----------------------------
-- Table structure for inventory_audit
-- ----------------------------
DROP TABLE IF EXISTS `inventory_audit`;
CREATE TABLE `inventory_audit` (
  `id` bigint NOT NULL COMMENT '主键',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `counted_qty` int NOT NULL,
  `current_in_stock_qty` int NOT NULL,
  `diff_qty` int NOT NULL,
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_inventory_audit_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_inventory_audit_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_inventory_audit_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_inventory_audit_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_inventory_audit_product_id` (`product_id`) USING BTREE,
  KEY `ix_inventory_audit_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_inventory_audit_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='库存盘点';

-- ----------------------------
-- Table structure for inventory_locked
-- ----------------------------
DROP TABLE IF EXISTS `inventory_locked`;
CREATE TABLE `inventory_locked` (
  `id` bigint NOT NULL COMMENT '主键',
  `ref_table_id` bigint NOT NULL COMMENT '关联表Id',
  `ref_table_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `qty` int NOT NULL COMMENT '数量',
  `finish_qty` int NOT NULL,
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `ref_table_ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '关联表唯一码',
  `locked_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '库存锁定状态 不能直接使用该字段做Where etc',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `ref_table_show_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '展示关联名称',
  `ref_table_show_ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '展示关联RefNum',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_locked_inventory_product_id` (`product_id`) USING BTREE,
  KEY `ix_locked_inventory_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_locked_inventory_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='锁定库存';

-- ----------------------------
-- Table structure for inventory_reserve
-- ----------------------------
DROP TABLE IF EXISTS `inventory_reserve`;
CREATE TABLE `inventory_reserve` (
  `id` bigint NOT NULL COMMENT '主键',
  `qty` int NOT NULL COMMENT '数量',
  `finish_qty` int NOT NULL,
  `reserve_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ref_table_id` bigint NOT NULL,
  `ref_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ref_table_ref_num` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ref_table_show_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ref_table_show_ref_num` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `reserve_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `reserve_qty` int NOT NULL COMMENT 'prep做完的数量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_reserve_inventory_product_id` (`product_id`) USING BTREE,
  KEY `ix_reserve_inventory_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_reserve_inventory_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_reserve_inventory_ref_table_id` (`ref_table_id`) USING BTREE,
  KEY `ix_reserve_inventory_ref_table_ref_num` (`ref_table_ref_num`) USING BTREE,
  KEY `ix_reserve_inventory_ref_table_show_ref_num` (`ref_table_show_ref_num`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='预留库存';

-- ----------------------------
-- Table structure for new_table
-- ----------------------------
DROP TABLE IF EXISTS `new_table`;
CREATE TABLE `new_table` (
  `column_name` varchar(255) NOT NULL,
  `column_comment` text,
  `is_nullable` varchar(3) DEFAULT NULL,
  `column_type` varchar(255) DEFAULT NULL,
  `column_default` text,
  PRIMARY KEY (`column_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for otb_package
-- ----------------------------
DROP TABLE IF EXISTS `otb_package`;
CREATE TABLE `otb_package` (
  `id` bigint NOT NULL COMMENT '主键',
  `sscc_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Serial Shipping Container Code，序列化货运容器代码',
  `carton_size_length` decimal(18,3) NOT NULL COMMENT '入库箱子-长',
  `carton_size_width` decimal(18,3) NOT NULL COMMENT '入库箱子-宽',
  `carton_size_height` decimal(18,3) NOT NULL COMMENT '入库箱子-高',
  `carton_size_weight` decimal(18,3) NOT NULL COMMENT '入库箱子-重量',
  `carton_size_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '入库箱子-重量单位',
  `carton_size_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '入库箱子-长度单位',
  `line_num` int NOT NULL DEFAULT '0' COMMENT '行序号',
  `otb_request_id` bigint NOT NULL COMMENT 'otb请求id',
  `otb_workorder_id` bigint NOT NULL COMMENT 'otb工单id',
  `otb_shipment_id` bigint DEFAULT NULL COMMENT 'otb装运id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `otb_pallet_id` bigint DEFAULT NULL COMMENT 'otb托盘id',
  `order_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '订单号',
  `otb_package_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb包裹状态',
  `otb_package_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb包裹类型',
  `otb_pallet_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb托盘类型',
  `otb_picking_slip_id` bigint NOT NULL COMMENT 'otb拣货单id',
  `ship_from_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址1',
  `ship_from_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址2',
  `ship_from_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址3',
  `ship_from_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址城市',
  `ship_from_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址公司',
  `ship_from_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址国家',
  `ship_from_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址邮箱',
  `ship_from_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '发货地址是否为住宅',
  `ship_from_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址名称',
  `ship_from_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址备注',
  `ship_from_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址电话',
  `ship_from_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址州/省',
  `ship_from_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址邮编',
  `ship_to_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址1',
  `ship_to_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址2',
  `ship_to_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址3',
  `ship_to_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址城市',
  `ship_to_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址公司',
  `ship_to_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址国家',
  `ship_to_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮箱',
  `ship_to_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '收货地址是否为住宅',
  `ship_to_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址名称',
  `ship_to_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址备注',
  `ship_to_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址电话',
  `ship_to_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址州/省',
  `ship_to_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮编',
  `short_ssccnum` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '短ssccnum',
  `station` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货站',
  `ship_carrier` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输公司',
  `ship_method` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输方式',
  `ship_api_profile_ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递Api配置RefNum',
  `tracking_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递号',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `signature_type` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `insurance_amount_currency` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `insurance_amount_amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otb_package_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otb_package_shipment_id` (`otb_shipment_id`) USING BTREE,
  KEY `ix_otb_package_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otb_package_request_id` (`otb_request_id`) USING BTREE,
  KEY `ix_otb_package_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_package_workorder_id` (`otb_workorder_id`) USING BTREE,
  KEY `ix_otb_package_pallet_id` (`otb_pallet_id`) USING BTREE,
  KEY `ix_otb_package_picking_slip_id` (`otb_picking_slip_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB包裹';

-- ----------------------------
-- Table structure for otb_package_detail
-- ----------------------------
DROP TABLE IF EXISTS `otb_package_detail`;
CREATE TABLE `otb_package_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otb_package_id` bigint NOT NULL COMMENT 'otb包裹id',
  `product_channel_sku` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品渠道sku',
  `product_barcode` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '渠道要求的需要贴的产品标识码',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_package_detail_header_id` (`otb_package_id`) USING BTREE,
  KEY `ix_otb_package_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_package_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB包裹详情';

-- ----------------------------
-- Table structure for otb_package_label
-- ----------------------------
DROP TABLE IF EXISTS `otb_package_label`;
CREATE TABLE `otb_package_label` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `label_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '面单类型',
  `label_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'label RefNum',
  `paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '纸张类型',
  `raw_data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `label_raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'label数据类型',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `otb_package_id` bigint NOT NULL COMMENT 'otb包裹id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `file_id_raw_data_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件系统数据类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_package_label_header_id` (`otb_package_id`) USING BTREE,
  KEY `ix_otb_package_label_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB包裹标签';

-- ----------------------------
-- Table structure for otb_pallet
-- ----------------------------
DROP TABLE IF EXISTS `otb_pallet`;
CREATE TABLE `otb_pallet` (
  `id` bigint NOT NULL COMMENT '主键',
  `sscc_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Serial Shipping Container Code，序列化货运容器代码',
  `pallet_size_length` decimal(18,3) NOT NULL COMMENT '托盘-长',
  `pallet_size_width` decimal(18,3) NOT NULL COMMENT '托盘-宽',
  `pallet_size_height` decimal(18,3) NOT NULL COMMENT '托盘-高',
  `pallet_size_weight` decimal(18,3) NOT NULL COMMENT '托盘-重量',
  `pallet_size_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '托盘-重量单位',
  `pallet_size_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '托盘-长度单位',
  `otb_request_id` bigint NOT NULL COMMENT 'otb请求id',
  `otb_workorder_id` bigint NOT NULL COMMENT 'otb工单id',
  `otb_shipment_id` bigint DEFAULT NULL COMMENT 'otb装运id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `short_ssccnum` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '短ssccnum',
  `otb_pallet_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb 托盘状态',
  `order_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '订单号',
  `ship_from_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址1',
  `ship_from_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址2',
  `ship_from_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址3',
  `ship_from_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址城市',
  `ship_from_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址公司',
  `ship_from_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址国家',
  `ship_from_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址邮箱',
  `ship_from_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '发货地址是否为住宅',
  `ship_from_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址名称',
  `ship_from_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址备注',
  `ship_from_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址电话',
  `ship_from_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址州/省',
  `ship_from_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址邮编',
  `ship_to_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址1',
  `ship_to_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址2',
  `ship_to_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址3',
  `ship_to_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址城市',
  `ship_to_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址公司',
  `ship_to_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址国家',
  `ship_to_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮箱',
  `ship_to_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '收货地址是否为住宅',
  `ship_to_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址名称',
  `ship_to_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址备注',
  `ship_to_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址电话',
  `ship_to_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址州/省',
  `ship_to_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮编',
  `pallet_empty_profile_id` bigint NOT NULL COMMENT '空托盘配置id',
  `pallet_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '托盘类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `otb_picking_slip_id` bigint NOT NULL COMMENT '拣货单id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otb_pallet_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otb_pallet_pallet_empty_profile_id` (`pallet_empty_profile_id`) USING BTREE,
  KEY `ix_otb_pallet_shipment_id` (`otb_shipment_id`) USING BTREE,
  KEY `ix_otb_pallet_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otb_pallet_request_id` (`otb_request_id`) USING BTREE,
  KEY `ix_otb_pallet_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_pallet_workorder_id` (`otb_workorder_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB托盘';

-- ----------------------------
-- Table structure for otb_pallet_detail
-- ----------------------------
DROP TABLE IF EXISTS `otb_pallet_detail`;
CREATE TABLE `otb_pallet_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `qty` int NOT NULL COMMENT '数量',
  `product_channel_sku` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道sku',
  `product_barcode` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '渠道要求的需要贴的产品标识码',
  `line_num` int NOT NULL COMMENT '行序号',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otb_pallet_id` bigint NOT NULL COMMENT 'otb托盘id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_pallet_detail_header_id` (`otb_pallet_id`) USING BTREE,
  KEY `ix_otb_pallet_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_pallet_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB托盘详情';

-- ----------------------------
-- Table structure for otb_pallet_label
-- ----------------------------
DROP TABLE IF EXISTS `otb_pallet_label`;
CREATE TABLE `otb_pallet_label` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `label_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '面单类型',
  `label_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'label RefNum',
  `paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '纸张类型',
  `raw_data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `label_raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'label数据类型',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `otb_pallet_id` bigint NOT NULL COMMENT 'otb托盘id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `file_id_raw_data_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件系统数据类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_pallet_label_header_id` (`otb_pallet_id`) USING BTREE,
  KEY `ix_otb_pallet_label_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB托盘标签';

-- ----------------------------
-- Table structure for otb_picking_slip
-- ----------------------------
DROP TABLE IF EXISTS `otb_picking_slip`;
CREATE TABLE `otb_picking_slip` (
  `id` bigint NOT NULL COMMENT '主键',
  `assigned_user_id` bigint NOT NULL COMMENT '分配人',
  `pick_to_station` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拣货到那里',
  `note` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `otb_picking_slip_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb 拣货单状态',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `otb_workorder_id` bigint NOT NULL COMMENT 'otb工单id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `picking_slip_product_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'Normal',
  `otb_picking_slip_type` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `build_from_type` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL,
  `pick_from_type` varchar(64) COLLATE utf8mb4_bin DEFAULT 'None',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otb_picking_slip_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otb_picking_slip_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otb_picking_slip_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_picking_slip_workorder_id` (`otb_workorder_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='otb拣货单';

-- ----------------------------
-- Table structure for otb_picking_slip_detail
-- ----------------------------
DROP TABLE IF EXISTS `otb_picking_slip_detail`;
CREATE TABLE `otb_picking_slip_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `qty` int NOT NULL COMMENT '数量',
  `picked_qty` int NOT NULL COMMENT '拣货数量',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `note` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `otb_picking_slip_id` bigint NOT NULL COMMENT 'otb拣货单id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `bin_location_detail_locked_id` bigint NOT NULL COMMENT '库位详情锁id',
  `packed_qty` int NOT NULL COMMENT '打包为Package 数量',
  `product_barcode` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '渠道要求的需要贴的产品标识码',
  `relabel_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'relabel状态',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `product_channel_sku` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_picking_slip_detail_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_otb_picking_slip_detail_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otb_picking_slip_detail_header_id` (`otb_picking_slip_id`) USING BTREE,
  KEY `ix_otb_picking_slip_detail_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_otb_picking_slip_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_picking_slip_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_picking_slip_detail_locked_bin_location_detail_id` (`bin_location_detail_locked_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='otb拣货单详情';

-- ----------------------------
-- Table structure for otb_prep_picking_slip
-- ----------------------------
DROP TABLE IF EXISTS `otb_prep_picking_slip`;
CREATE TABLE `otb_prep_picking_slip` (
  `id` bigint NOT NULL COMMENT '主键',
  `assigned_user_id` bigint NOT NULL COMMENT '分配人',
  `pick_to_station` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拣货到那里',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `otb_prep_picking_slip_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb预拣货单状态',
  `otb_prep_picking_slip_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb预拣货单类型',
  `note` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `otb_picking_slip_id` bigint DEFAULT NULL COMMENT 'otb拣货单id',
  `qty` int NOT NULL COMMENT '数量',
  `allocate_putaway_qty` int NOT NULL COMMENT '已经分配的上架数量',
  `putaway_qty` int NOT NULL COMMENT '上架数量',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '描述',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `product_barcode` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '渠道要求的需要贴的产品标识码',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `product_channel_sku` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '渠道要求的需要贴的产品标识SKU',
  `transaction_partner_id` bigint NOT NULL COMMENT '产品所需供应商id',
  `prep_picking_slip_version_int` int NOT NULL COMMENT '预工单产品版本',
  `prep_picking_slip_product_type` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT 'otb预拣货单产品类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otb_prep_picking_slip_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otb_prep_picking_slip_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otb_prep_picking_slip_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otb_prep_picking_slip_picking_slip_id` (`otb_picking_slip_id`) USING BTREE,
  KEY `ix_otb_prep_picking_slip_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_prep_picking_slip_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='otb预拣货单';

-- ----------------------------
-- Table structure for otb_prep_picking_slip_detail
-- ----------------------------
DROP TABLE IF EXISTS `otb_prep_picking_slip_detail`;
CREATE TABLE `otb_prep_picking_slip_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `qty` int NOT NULL COMMENT '数量',
  `allocate_qty` int NOT NULL COMMENT '已经分配的数量',
  `picked_qty` int NOT NULL COMMENT '拣货数量',
  `bin_location_detail_locked_id` bigint NOT NULL COMMENT '库位详情锁id',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `note` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `otb_prep_picking_slip_id` bigint NOT NULL COMMENT 'otb预拣货单id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `putaway_qty` int NOT NULL COMMENT '上架数量',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_prep_ps_detail_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_otb_prep_ps_detail_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otb_prep_ps_detail_header_id` (`otb_prep_picking_slip_id`) USING BTREE,
  KEY `ix_otb_prep_ps_detail_locked_bin_location_detail_id` (`bin_location_detail_locked_id`) USING BTREE,
  KEY `ix_otb_prep_ps_detail_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_otb_prep_ps_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_prep_ps_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='otb预拣货单详情';

-- ----------------------------
-- Table structure for otb_prep_workorder
-- ----------------------------
DROP TABLE IF EXISTS `otb_prep_workorder`;
CREATE TABLE `otb_prep_workorder` (
  `id` bigint NOT NULL COMMENT '主键',
  `otb_workorder_id` bigint NOT NULL COMMENT 'otb工单id',
  `otb_workorder_detail_id` bigint NOT NULL COMMENT 'otb 工单详情id',
  `qty` int NOT NULL COMMENT '数量',
  `inventory_reserve_id` bigint NOT NULL COMMENT '库存预定id',
  `putaway_qty` int NOT NULL COMMENT '上架数量',
  `otb_prep_workorder_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb预工单状态',
  `prep_workorder_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '预工单类型',
  `otb_prep_picking_slip_id` bigint DEFAULT NULL COMMENT 'otb预拣货单id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `product_barcode` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '渠道要求的需要贴的产品标识码',
  `bin_location_id` bigint DEFAULT NULL COMMENT '库位id',
  `prep_workorder_version_int` int NOT NULL COMMENT '预工单产品版本',
  `otb_request_id` bigint NOT NULL COMMENT 'otb请求id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `product_channel_sku` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '渠道要求的需要贴的产品标识SKU',
  `transaction_partner_id` bigint NOT NULL COMMENT '产品所需供应商id',
  `prep_workorder_product_type` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '工单产品类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otb_prep_wo_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otb_prep_wo_prep_picking_slip_id` (`otb_prep_picking_slip_id`) USING BTREE,
  KEY `ix_otb_prep_wo_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otb_prep_wo_reserve_inventory_id` (`inventory_reserve_id`) USING BTREE,
  KEY `ix_otb_prep_wo_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_prep_wo_workorder_detail_id` (`otb_workorder_detail_id`) USING BTREE,
  KEY `ix_otb_prep_wo_workorder_id` (`otb_workorder_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otb_prep_wo_request_id` (`otb_request_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB预提工单';

-- ----------------------------
-- Table structure for otb_prep_workorder_bin_location
-- ----------------------------
DROP TABLE IF EXISTS `otb_prep_workorder_bin_location`;
CREATE TABLE `otb_prep_workorder_bin_location` (
  `id` bigint NOT NULL,
  `otb_workorder_id` bigint NOT NULL,
  `otb_workorder_detail_id` bigint NOT NULL,
  `otb_prep_workorder_id` bigint NOT NULL,
  `otb_prep_workorder_detail_id` bigint NOT NULL,
  `otb_prep_picking_slip_id` bigint NOT NULL,
  `otb_prep_picking_slip_detail_id` bigint NOT NULL,
  `bin_location_detail_id` bigint NOT NULL,
  `bin_location_id` bigint NOT NULL,
  `qty` int NOT NULL,
  `version` bigint NOT NULL,
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `tenant_id` bigint NOT NULL,
  `warehouse_id` bigint NOT NULL,
  `product_id` bigint NOT NULL,
  `create_by` bigint NOT NULL,
  `create_time` timestamp NOT NULL,
  `update_by` bigint DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `remove_flag` tinyint(1) NOT NULL,
  `bin_location_detail_locked_id` bigint NOT NULL COMMENT '库位详情锁id',
  `product_version_id` bigint NOT NULL COMMENT '产品版本id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_partner_warehouse` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_product_latest_id` (`product_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_sub_picking_slip_detail_id` (`otb_prep_picking_slip_detail_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_sub_picking_slip_id` (`otb_prep_picking_slip_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_sub_workorder_detail_id` (`otb_prep_workorder_detail_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_sub_workorder_id` (`otb_prep_workorder_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_workorder_detail_id` (`otb_workorder_detail_id`) USING BTREE,
  KEY `ix_otb_prep_wo_bl_workorder_id` (`otb_workorder_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for otb_prep_workorder_detail
-- ----------------------------
DROP TABLE IF EXISTS `otb_prep_workorder_detail`;
CREATE TABLE `otb_prep_workorder_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `qty` int NOT NULL COMMENT '数量',
  `line_num` int NOT NULL COMMENT '行序号',
  `inventory_locked_id` bigint DEFAULT NULL COMMENT '库存锁定id',
  `picked_qty` int NOT NULL COMMENT '拣货数量',
  `putaway_qty` int NOT NULL COMMENT '上架数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otb_prep_workorder_id` bigint NOT NULL COMMENT 'otb预工单id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `prep_workorder_detail_type` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '预工单详情类型',
  `prep_workorder_detail_version_int` int DEFAULT NULL COMMENT '预工单详情产品版本',
  `parent_id` bigint DEFAULT NULL COMMENT '父节点id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_prep_workorder_detail_sub_workorder_detail_id` (`id`) USING BTREE,
  KEY `ix_otb_prep_wo_detail_header_id` (`otb_prep_workorder_id`) USING BTREE,
  KEY `ix_otb_prep_wo_detail_locked_inventory_id` (`inventory_locked_id`) USING BTREE,
  KEY `ix_otb_prep_wo_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_prep_wo_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB预提工单详情';

-- ----------------------------
-- Table structure for otb_request
-- ----------------------------
DROP TABLE IF EXISTS `otb_request`;
CREATE TABLE `otb_request` (
  `id` bigint NOT NULL COMMENT '主键',
  `request_ref_num` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '外部唯一标识码',
  `channel` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '渠道',
  `ship_window_start` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发货窗口开始时间',
  `otb_request_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求状态',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴Id',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `request_shipment_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '请求发货状态',
  `order_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '订单号',
  `ship_to_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址1',
  `ship_to_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址2',
  `ship_to_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址3',
  `ship_to_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址城市',
  `ship_to_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址公司',
  `ship_to_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址国家',
  `ship_to_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮箱',
  `ship_to_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '收货地址是否为住宅',
  `ship_to_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址名称',
  `ship_to_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址备注',
  `ship_to_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址电话',
  `ship_to_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址州/省',
  `ship_to_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮编',
  `ship_window_end` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发货窗口结束时间',
  `ship_from_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址1',
  `ship_from_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址2',
  `ship_from_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址3',
  `ship_from_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址城市',
  `ship_from_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址公司',
  `ship_from_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址国家',
  `ship_from_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址邮箱',
  `ship_from_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '是否住宅地址',
  `ship_from_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '发货人姓名',
  `ship_from_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货备注',
  `ship_from_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货电话',
  `ship_from_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '发货州',
  `ship_from_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货邮编',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `signature_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '签名类型',
  `insurance_amount_currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保险金货币',
  `insurance_amount_amount` decimal(10,2) DEFAULT NULL COMMENT '保险金额',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otb_request_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otb_request_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otb_request_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_request_transaction_partner_id_request_ref_num` (`transaction_partner_id`,`request_ref_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB请求';

-- ----------------------------
-- Table structure for otb_request_detail
-- ----------------------------
DROP TABLE IF EXISTS `otb_request_detail`;
CREATE TABLE `otb_request_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `otb_request_id` bigint NOT NULL COMMENT 'otb请求id',
  `qty` int NOT NULL COMMENT '数量',
  `line_num` int NOT NULL COMMENT '行序号',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `product_barcode` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '渠道要求的需要贴的产品标识码',
  `product_channel_sku` varchar(512) COLLATE utf8mb4_bin NOT NULL COMMENT '产品渠道流水号',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_request_detail_header_id` (`otb_request_id`) USING BTREE,
  KEY `ix_otb_request_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_request_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB请求详情';

-- ----------------------------
-- Table structure for otb_routing_instruction
-- ----------------------------
DROP TABLE IF EXISTS `otb_routing_instruction`;
CREATE TABLE `otb_routing_instruction` (
  `id` bigint NOT NULL COMMENT '主键',
  `carrier_contact_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '承运人联系人姓名',
  `carrier_contact_phone` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '承运人联系人电话',
  `carrier_contact_email` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '承运人联系人邮箱',
  `receiving_contact_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货人联系人姓名',
  `receiving_contact_phone` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货人联系人电话',
  `receiving_contact_email` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货人联系人邮箱',
  `shipper_contact_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货人联系人姓名',
  `shipper_contact_phone` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货人联系人电话',
  `shipper_contact_email` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货人联系人邮箱',
  `ship_from_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址名称',
  `ship_from_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址公司',
  `ship_from_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址国家',
  `ship_from_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址州/省',
  `ship_from_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址城市',
  `ship_from_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址邮编',
  `ship_from_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址1',
  `ship_from_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址2',
  `ship_from_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址3',
  `ship_from_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址邮箱',
  `ship_from_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址电话',
  `ship_from_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址备注',
  `ship_from_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '发货地址是否为住宅地址',
  `ship_to_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址名称',
  `ship_to_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址公司',
  `ship_to_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址国家',
  `ship_to_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址州/省',
  `ship_to_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址城市',
  `ship_to_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮编',
  `ship_to_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址1',
  `ship_to_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址2',
  `ship_to_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址3',
  `ship_to_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮箱',
  `ship_to_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址电话',
  `ship_to_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址备注',
  `ship_to_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '收货地址是否为住宅',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `bol_file_file_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'BOL文件数据',
  `bol_file_file_extension` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'BOL文件扩展名',
  `bol_file_file_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'BOL文件类型',
  `carrier_code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '承运人代码',
  `carrier_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '承运人名称',
  `carton_qty` int NOT NULL DEFAULT '0' COMMENT 'Carton数量',
  `confirmed_shipment_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '确认的运输类型',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `pallet_qty` int NOT NULL DEFAULT '0' COMMENT '托盘数量',
  `otb_request_id` bigint NOT NULL COMMENT 'otb请求id',
  `request_pickup_date` timestamp NULL DEFAULT NULL COMMENT '请求提货日期',
  `schedule_pickup_date` timestamp NULL DEFAULT NULL COMMENT '计划提货日期',
  `otb_shipment_id` bigint NOT NULL COMMENT 'otb装运id',
  `otb_shipment_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '运输参考编号',
  `stacked_pallet_qty` int NOT NULL DEFAULT '0' COMMENT '可堆叠托盘数量',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `bol_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'BOL编号',
  `ship_carrier` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输公司',
  `ship_method` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输方式',
  `ship_api_profile_ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递Api配置RefNum',
  `otb_routing_instruction_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '路由指令状态',
  `otb_pallet_file_file_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '托盘文件数据',
  `otb_pallet_file_file_extension` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '托盘文件扩展名',
  `otb_pallet_file_file_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '托盘文件类型',
  `otb_bol_file_paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'BOL文件纸张类型',
  `otb_pallet_file_paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '托盘文件纸张类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `signature_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `insurance_amount_currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `insurance_amount_amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otb_inst_ref_num` (`ref_num`) USING BTREE COMMENT '参考编号索引',
  KEY `ix_otb_inst_partner_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE COMMENT '租户ID和仓库ID索引',
  KEY `ix_otb_inst_warehouse_id` (`warehouse_id`) USING BTREE COMMENT '仓库ID索引',
  KEY `ix_otb_inst_request_id` (`otb_request_id`) USING BTREE COMMENT '请求ID索引',
  KEY `ix_otb_inst_shipment_id` (`otb_shipment_id`) USING BTREE COMMENT '运输ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='otb发货指南';

-- ----------------------------
-- Table structure for otb_routing_instruction_package_label
-- ----------------------------
DROP TABLE IF EXISTS `otb_routing_instruction_package_label`;
CREATE TABLE `otb_routing_instruction_package_label` (
  `id` bigint NOT NULL COMMENT '主键',
  `otb_routing_instruction_id` bigint NOT NULL COMMENT '路由指令ID',
  `package_ssccnum` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '包裹SSCC编号',
  `label_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '面单类型',
  `label_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'label RefNum',
  `paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '纸张类型',
  `raw_data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `file_id_raw_data_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件系统数据类型',
  `label_raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'label数据类型',
  `line_num` int NOT NULL COMMENT '行序号',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_inst_package_label_owner_warehouse` (`tenant_id`,`warehouse_id`) USING BTREE COMMENT '租户ID和仓库ID索引',
  KEY `ix_otb_inst_package_label_routing_instruction_id` (`otb_routing_instruction_id`) USING BTREE COMMENT '路由指令ID索引',
  KEY `ix_otb_inst_package_label_warehouse_id` (`warehouse_id`) USING BTREE COMMENT '仓库ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='otb发货指南包裹标签';

-- ----------------------------
-- Table structure for otb_routing_instruction_pallet_label
-- ----------------------------
DROP TABLE IF EXISTS `otb_routing_instruction_pallet_label`;
CREATE TABLE `otb_routing_instruction_pallet_label` (
  `id` bigint NOT NULL COMMENT '主键',
  `otb_routing_instruction_id` bigint NOT NULL COMMENT '路由指令ID',
  `pallet_ssccnum` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '托盘SSCC编号',
  `label_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '面单类型',
  `label_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'label RefNum',
  `paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '纸张类型',
  `raw_data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `file_id_raw_data_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件系统数据类型',
  `label_raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'label数据类型',
  `line_num` int NOT NULL COMMENT '行序号',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_inst_pallet_label_owner_warehouse` (`tenant_id`,`warehouse_id`) USING BTREE COMMENT '租户ID和仓库ID索引',
  KEY `ix_otb_inst_pallet_label_routing_instruction_id` (`otb_routing_instruction_id`) USING BTREE COMMENT '路由指令ID索引',
  KEY `ix_otb_inst_pallet_label_warehouse_id` (`warehouse_id`) USING BTREE COMMENT '仓库ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='otb发货指南托盘标签';

-- ----------------------------
-- Table structure for otb_shipment
-- ----------------------------
DROP TABLE IF EXISTS `otb_shipment`;
CREATE TABLE `otb_shipment` (
  `id` bigint NOT NULL COMMENT '发货时间',
  `otb_request_id` bigint NOT NULL COMMENT 'otb请求id',
  `otb_workorder_id` bigint NOT NULL COMMENT 'otb工单id',
  `otb_shipment_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb 装运状态',
  `otb_shipment_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb 装运类型',
  `otb_picking_slip_id` bigint NOT NULL COMMENT 'otb拣货单id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `bol_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '提货单编号',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `signed_bol_file_file_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '签名的Bol文件数据',
  `signed_bol_file_file_extension` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '签名的Bol文件拓展名',
  `signed_bol_file_file_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '签名的Bol文件类型',
  `package_count` int NOT NULL COMMENT '包裹总数',
  `pallet_count` int NOT NULL COMMENT '托盘总数',
  `total_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '重量单位',
  `total_weight_value` decimal(18,3) NOT NULL COMMENT '重量',
  `volume_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '体积单位',
  `volume_value` decimal(18,3) NOT NULL COMMENT '体积',
  `signed_bol_file_paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '签名的Bol文件纸张类型',
  `pallet_file_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '托盘文件状态',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `routing_instruction_id` bigint DEFAULT NULL COMMENT 'otb路径id',
  `shipped_time` datetime DEFAULT NULL COMMENT '发货时间',
  `routing_instruction_request_pickup_date` datetime DEFAULT NULL,
  `routing_instruction_schedule_pickup_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otb_shipment_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otb_shipment_otb_picking_slip_id` (`otb_picking_slip_id`) USING BTREE,
  KEY `ix_otb_shipment_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otb_shipment_request_id` (`otb_request_id`) USING BTREE,
  KEY `ix_otb_shipment_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_shipment_workorder_id` (`otb_workorder_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB装运';

-- ----------------------------
-- Table structure for otb_shipment_detail
-- ----------------------------
DROP TABLE IF EXISTS `otb_shipment_detail`;
CREATE TABLE `otb_shipment_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `product_channel_sku` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道sku',
  `product_barcode` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '渠道要求的需要贴的产品标识码',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otb_shipment_id` bigint NOT NULL COMMENT 'otb装运id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_shipment_detail_header_id` (`otb_shipment_id`) USING BTREE,
  KEY `ix_otb_shipment_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_shipment_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB装运详情';

-- ----------------------------
-- Table structure for otb_workorder
-- ----------------------------
DROP TABLE IF EXISTS `otb_workorder`;
CREATE TABLE `otb_workorder` (
  `id` bigint NOT NULL COMMENT '主键',
  `request_snapshot_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'request快照RefNum',
  `request_snapshot_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'request快照备注',
  `request_snapshot_channel` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'request快照渠道',
  `request_snapshot_transaction_partner_id` bigint NOT NULL COMMENT 'request快照transctionParterId',
  `request_snapshot_request_ref_num` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求快照RequestRefnum',
  `otb_workorder_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'otb 工单状态',
  `otb_request_id` bigint NOT NULL COMMENT 'otb请求id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `otb_request_shipment_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'otb 请求装运状态',
  `workorder_prep_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'otb 工单 预状态',
  `request_snapshot_ship_window_start` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请求快照 发货窗口开始时间',
  `request_snapshot_order_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '请求快照 订单号',
  `request_snapshot_ship_window_end` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请求快照 发货窗口结束时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `workorder_product_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品类型',
  `otb_workorder_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单类型',
  `otb_picking_slip_id` bigint DEFAULT NULL COMMENT '拣货单id',
  `request_snapshot_insurance_amount_currency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求快照保险金货币',
  `request_snapshot_insurance_amount_amount` decimal(18,3) DEFAULT NULL COMMENT '请求快照保险金额',
  `request_snapshot_signature_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求快照签名类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otb_workorder_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otb_workorder_request_id` (`otb_request_id`) USING BTREE,
  KEY `ix_otb_workorder_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otb_workorder_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB工单';

-- ----------------------------
-- Table structure for otb_workorder_bin_location
-- ----------------------------
DROP TABLE IF EXISTS `otb_workorder_bin_location`;
CREATE TABLE `otb_workorder_bin_location` (
  `id` bigint NOT NULL COMMENT '主键',
  `otb_workorder_id` bigint NOT NULL COMMENT '发货到b端工单id',
  `otb_workorder_detail_id` bigint NOT NULL COMMENT '发货到b端工单详情id',
  `otb_picking_slip_id` bigint NOT NULL COMMENT '发货到b端拣货id',
  `otb_picking_slip_detail_id` bigint NOT NULL COMMENT '发货到b端拣货详情id',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `product_version_id` bigint NOT NULL COMMENT '产品版本id',
  `bin_location_detail_locked_id` bigint NOT NULL COMMENT '库位详情锁id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_workorder_bl_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_otb_workorder_bl_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otb_workorder_bl_out_bound_to_cpicking_slip_detail_id` (`otb_picking_slip_detail_id`) USING BTREE,
  KEY `ix_otb_workorder_bl_out_bound_to_cpicking_slip_id` (`otb_picking_slip_id`) USING BTREE,
  KEY `ix_otb_workorder_bl_out_bound_to_cworkorder_detail_id` (`otb_workorder_detail_id`) USING BTREE,
  KEY `ix_otb_workorder_bl_out_bound_to_cworkorder_id` (`otb_workorder_id`) USING BTREE,
  KEY `ix_otb_workorder_bl_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_workorder_bl_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_workorder_bl_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='OTB工单仓储位置';

-- ----------------------------
-- Table structure for otb_workorder_detail
-- ----------------------------
DROP TABLE IF EXISTS `otb_workorder_detail`;
CREATE TABLE `otb_workorder_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `picked_qty` int NOT NULL DEFAULT '0' COMMENT '拣货数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otb_workorder_id` bigint NOT NULL COMMENT 'otb工单id',
  `line_num` int NOT NULL COMMENT '行序号',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `qty` int NOT NULL DEFAULT '0' COMMENT '数量',
  `finish_qty` int NOT NULL DEFAULT '0' COMMENT '完成数量',
  `detail_snapshot_product_barcode` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '请求快照,渠道产品编码',
  `inventory_locked_id` bigint NOT NULL COMMENT '库存锁定id',
  `inventory_reserve_id` bigint DEFAULT NULL COMMENT '库存预定id',
  `reserve_qty` int NOT NULL DEFAULT '0' COMMENT '预定数量',
  `packed_qty` int NOT NULL DEFAULT '0' COMMENT '打包为Package 数量',
  `shipment_qty` int NOT NULL DEFAULT '0' COMMENT '做成Shipment数量',
  `finish_reserve_qty` int NOT NULL DEFAULT '0' COMMENT '完成预定数量',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `detail_snapshot_product_channel_sku` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `shipped_qty` int NOT NULL DEFAULT '0' COMMENT 'ShippedQty',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otb_workorder_detail_header_id` (`otb_workorder_id`) USING BTREE,
  KEY `ix_otb_workorder_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otb_workorder_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otb_workorder_detail_locked_inventory_id` (`inventory_locked_id`) USING BTREE,
  KEY `ix_otb_workorder_detail_reserve_inventory_id` (`inventory_reserve_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTB工单详情';

-- ----------------------------
-- Table structure for otb_workorder_detail_bin_location
-- ----------------------------
DROP TABLE IF EXISTS `otb_workorder_detail_bin_location`;
CREATE TABLE `otb_workorder_detail_bin_location` (
  `id` bigint NOT NULL COMMENT '主键id',
  `otb_workorder_id` bigint NOT NULL COMMENT '工单id',
  `pick_from_bin_location_detail_id` bigint NOT NULL COMMENT '从那个库位上拣货',
  `pick_from_bin_location_id` bigint NOT NULL COMMENT '从那个库位上拣货',
  `pick_to_bin_location_detail_id` bigint NOT NULL COMMENT '拣货到那个库位上了',
  `pick_to_bin_location_id` bigint NOT NULL COMMENT '拣货到那个库位上了',
  `pick_to_pick_bin_location_id` bigint NOT NULL,
  `locked_binLocation_detail_id` bigint NOT NULL COMMENT '锁定库位详情Id',
  `picked_qty` bigint NOT NULL DEFAULT '0' COMMENT '拣货数量',
  `product_id` bigint NOT NULL COMMENT '产品头id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `version` bigint NOT NULL COMMENT '乐观锁',
  `create_by` bigint NOT NULL COMMENT '创建人id',
  `update_by` bigint NOT NULL COMMENT '更新人id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `remove_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for otc_package
-- ----------------------------
DROP TABLE IF EXISTS `otc_package`;
CREATE TABLE `otc_package` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `tracking_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递号',
  `insurance_amount_amount` decimal(18,3) DEFAULT NULL COMMENT '保险金额',
  `signature_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '签名类型',
  `ship_to_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '收件人名称',
  `ship_to_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件公司',
  `ship_to_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收件国家',
  `ship_to_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收件州',
  `ship_to_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收件城市',
  `ship_to_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件邮政编码',
  `ship_to_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收件地址1',
  `ship_to_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件地址2',
  `ship_to_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件地址3',
  `ship_to_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件邮件信息',
  `ship_to_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件电话信息',
  `ship_to_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件备注',
  `ship_from_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发件人名称',
  `ship_from_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件公司',
  `ship_from_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件国家',
  `ship_from_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件州',
  `ship_from_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件城市',
  `ship_from_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件邮政编码',
  `ship_from_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '发件地址1',
  `ship_from_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件地址2',
  `ship_from_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件地址3',
  `ship_from_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件邮件',
  `ship_from_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件电话',
  `ship_from_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件备注',
  `otc_workorder_id` bigint NOT NULL COMMENT 'c端出货工单id',
  `ship_express_flag` tinyint(1) NOT NULL COMMENT '是否为快递运输',
  `ship_method` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输方式',
  `ship_carrier` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输公司',
  `ship_size_length` decimal(18,3) NOT NULL COMMENT '运输箱子-长',
  `ship_size_width` decimal(18,3) NOT NULL COMMENT '运输箱子-宽',
  `ship_size_height` decimal(18,3) NOT NULL COMMENT '运输箱子-高',
  `ship_size_weight` decimal(18,3) NOT NULL COMMENT '运输箱子-重量',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `build_ship_strategy` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '构建运输策略',
  `package_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '包裹状态',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `otc_picking_slip_id` bigint DEFAULT NULL COMMENT 'c端拣货id',
  `ready_to_ship_time` timestamp NULL DEFAULT NULL COMMENT '准备运输时间',
  `shipped_time` timestamp NULL DEFAULT NULL COMMENT '已运输时间',
  `ship_from_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '住宅地址发件运输',
  `ship_size_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '运输箱子-长度单位',
  `ship_size_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '运输箱子-重量单位',
  `ship_to_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '收货地址是否为住宅',
  `insurance_amount_currency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保险金额货币',
  `ship_api_profile_ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递Api配置RefNum',
  `package_multibox_upc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '多盒包裹编码',
  `package_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '包裹类型',
  `package_multibox_line_num` int DEFAULT NULL COMMENT '多盒包裹行号',
  `package_multibox_product_id` bigint DEFAULT NULL COMMENT '多盒包裹产品新品id',
  `package_multibox_version_int` int DEFAULT NULL COMMENT '多盒包裹版本',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `process_type` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Normal' COMMENT '流程类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otc_package_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otc_package_out_bound_to_cworkorder_id` (`otc_workorder_id`) USING BTREE,
  KEY `ix_otc_package_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otc_package_out_bound_to_cpicking_slip_id` (`otc_picking_slip_id`) USING BTREE,
  KEY `ix_otc_package_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC包裹';

-- ----------------------------
-- Table structure for otc_package_bin_location
-- ----------------------------
DROP TABLE IF EXISTS `otc_package_bin_location`;
CREATE TABLE `otc_package_bin_location` (
  `id` bigint NOT NULL COMMENT '主键',
  `otc_package_id` bigint NOT NULL COMMENT '包裹id',
  `otc_package_detail_id` bigint NOT NULL COMMENT '包裹详情id',
  `otc_workorder_bin_location_id` bigint NOT NULL COMMENT '工单仓储位置id',
  `qty` int NOT NULL COMMENT '数量',
  `otc_picking_slip_id` bigint NOT NULL COMMENT '发货到c端拣货id',
  `otc_picking_slip_detail_id` bigint NOT NULL COMMENT '发货到c端拣货详情id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `product_version_id` bigint DEFAULT NULL COMMENT '产品版本id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otc_workorder_id` bigint NOT NULL COMMENT '发货到c端工单id',
  `otc_workorder_detail_id` bigint NOT NULL COMMENT '发货到c端工单详情id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='OTC包裹仓储位置';

-- ----------------------------
-- Table structure for otc_package_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_package_detail`;
CREATE TABLE `otc_package_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `otc_package_id` bigint NOT NULL COMMENT 'c端出货打包id',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `picked_qty` int NOT NULL COMMENT '拣货数量',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_package_detail_out_bound_to_cpackage_id` (`otc_package_id`) USING BTREE,
  KEY `ix_otc_package_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_package_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC包裹详情';

-- ----------------------------
-- Table structure for otc_package_label
-- ----------------------------
DROP TABLE IF EXISTS `otc_package_label`;
CREATE TABLE `otc_package_label` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `label_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '面单类型',
  `label_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'label RefNum',
  `paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '纸张类型',
  `label_raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'label数据类型',
  `otc_package_id` bigint NOT NULL COMMENT 'c端出货打包id',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `raw_data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `file_id_raw_data_type` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件系统数据类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_package_label_header_id` (`otc_package_id`) USING BTREE,
  KEY `ix_otc_package_label_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC包裹标签';

-- ----------------------------
-- Table structure for otc_picking_slip
-- ----------------------------
DROP TABLE IF EXISTS `otc_picking_slip`;
CREATE TABLE `otc_picking_slip` (
  `id` bigint NOT NULL COMMENT '主键',
  `assigned_user_id` bigint NOT NULL COMMENT '分配人',
  `pick_to_station` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拣货到那里',
  `picking_slip_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '拣货状态',
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `order_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单类型',
  `has_cus_ship_require` tinyint(1) NOT NULL COMMENT '是否有特定运输要求',
  `on_site_pack_flag` tinyint(1) NOT NULL COMMENT '现场包装标志',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `picking_slip_prep_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '拣货预处理类型',
  `locked_before` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '锁定前',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `transaction_partner_id` bigint NOT NULL DEFAULT '0' COMMENT '供应商id',
  `build_from_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '由谁构建类型',
  `pick_from_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'None' COMMENT '由谁拣货类型',
  `picking_slip_product_type` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Normal' COMMENT '产品类型',
  `process_type` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Normal' COMMENT '流程类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otc_ps_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otc_ps_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otc_ps_pick_to_station` (`pick_to_station`) USING BTREE,
  KEY `ix_otc_ps_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC拣货单';

-- ----------------------------
-- Table structure for otc_picking_slip_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_picking_slip_detail`;
CREATE TABLE `otc_picking_slip_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `qty` int NOT NULL COMMENT '数量',
  `picked_qty` int NOT NULL COMMENT '拣货数量',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `otc_picking_slip_id` bigint NOT NULL COMMENT '发货到c端拣货id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `ready_to_ship_qty` int NOT NULL,
  `bin_location_detail_locked_id` bigint NOT NULL COMMENT '库位详情锁id',
  `allocate_qty` int NOT NULL COMMENT '已经分配的数量',
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_ps_detail_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_otc_ps_detail_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otc_ps_detail_header_id` (`otc_picking_slip_id`) USING BTREE,
  KEY `ix_otc_ps_detail_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_otc_ps_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_ps_detail_locked_bin_location_detail_id` (`bin_location_detail_locked_id`) USING BTREE,
  KEY `ix_otc_ps_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC预拣货单详情';

-- ----------------------------
-- Table structure for otc_prep_picking_slip
-- ----------------------------
DROP TABLE IF EXISTS `otc_prep_picking_slip`;
CREATE TABLE `otc_prep_picking_slip` (
  `id` bigint NOT NULL COMMENT '主键',
  `assigned_user_id` bigint NOT NULL COMMENT '分配人',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `prep_picking_slip_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '预拣货状态',
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
  `print_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '打印状态',
  `qty` int NOT NULL COMMENT '数量',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '描述',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `allocate_putaway_qty` int NOT NULL DEFAULT '0' COMMENT '已经分配的上架数量',
  `putaway_qty` int NOT NULL DEFAULT '0' COMMENT '上架数量',
  `order_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '订单类型',
  `otc_picking_slip_id` bigint DEFAULT NULL COMMENT '拣货id',
  `pick_to_station` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拣货到那里',
  `has_cus_ship_require` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有特定运输要求',
  `on_site_pack_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '现场包装标志',
  `prep_picking_slip_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '预拣货类型',
  `locked_before` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '锁定前',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `transaction_partner_id` bigint NOT NULL DEFAULT '0' COMMENT '供应商id',
  `prep_picking_slip_version_int` int NOT NULL DEFAULT '0' COMMENT '产品版本号',
  `prep_picking_slip_product_type` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Normal' COMMENT '产品类型',
  `process_type` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Normal' COMMENT '流程类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otc_prep_ps_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otc_prep_ps_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otc_prep_ps_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_prep_ps_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otc_prep_ps_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otc_prep_ps_picking_slip_id` (`otc_picking_slip_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC预提货单';

-- ----------------------------
-- Table structure for otc_prep_picking_slip_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_prep_picking_slip_detail`;
CREATE TABLE `otc_prep_picking_slip_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `qty` int NOT NULL COMMENT '数量',
  `allocate_qty` int NOT NULL COMMENT '已经分配的数量',
  `picked_qty` int NOT NULL COMMENT '拣货数量',
  `bin_location_detail_locked_id` bigint NOT NULL COMMENT '库位详情锁id',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `otc_prep_picking_slip_id` bigint NOT NULL COMMENT '发货到c端预拣货id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `putaway_qty` int NOT NULL DEFAULT '0' COMMENT '上架数量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_prep_ps_detail_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_otc_prep_ps_detail_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otc_prep_ps_detail_header_id` (`otc_prep_picking_slip_id`) USING BTREE,
  KEY `ix_otc_prep_ps_detail_locked_bin_location_detail_id` (`bin_location_detail_locked_id`) USING BTREE,
  KEY `ix_otc_prep_ps_detail_product_version_id` (`product_version_id`) USING BTREE,
  KEY `ix_otc_prep_ps_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_prep_ps_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC预提货单详情';

-- ----------------------------
-- Table structure for otc_prep_putaway_slip
-- ----------------------------
DROP TABLE IF EXISTS `otc_prep_putaway_slip`;
CREATE TABLE `otc_prep_putaway_slip` (
  `id` bigint NOT NULL COMMENT '主键',
  `prep_workorder_id` bigint NOT NULL COMMENT 'Prep工单id',
  `prep_picking_slip_id` bigint NOT NULL COMMENT 'Prep拣货单id',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `ref_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
  `putaway_slip_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'New' COMMENT '状态',
  `putaway_slip_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '上架类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `udx_ref_num` (`ref_num`) USING BTREE COMMENT '编码唯一',
  KEY `idx_putaway_slip` (`tenant_id`,`warehouse_id`,`prep_workorder_id`) USING BTREE COMMENT '上架单索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC Prep上架单';

-- ----------------------------
-- Table structure for otc_prep_putaway_slip_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_prep_putaway_slip_detail`;
CREATE TABLE `otc_prep_putaway_slip_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `prep_workorder_bin_location_id` bigint NOT NULL COMMENT 'Prep工单分配仓储id',
  `prep_workorder_detail_id` bigint NOT NULL COMMENT 'Prep工单详情id',
  `prep_workorder_id` bigint NOT NULL COMMENT 'Prep工单id',
  `prep_picking_slip_id` bigint NOT NULL COMMENT 'Prep拣货单详情id',
  `prep_picking_slip_detail_id` bigint NOT NULL COMMENT 'Prep拣货单id',
  `source_bin_location_id` bigint NOT NULL COMMENT '库位id',
  `source_bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `dest_bin_location_id` bigint NOT NULL COMMENT '预计库位id',
  `dest_bin_location_detail_id` bigint NOT NULL COMMENT '预计库位详情id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `qty` int NOT NULL COMMENT '数量',
  `putaway_qty` int NOT NULL DEFAULT '0' COMMENT '上架数量',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `putaway_slip_id` bigint NOT NULL COMMENT '上架单id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_putaway_slip` (`tenant_id`,`warehouse_id`,`putaway_slip_id`) USING BTREE COMMENT '上架单详情索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC Prep上架单详情';

-- ----------------------------
-- Table structure for otc_prep_workorder
-- ----------------------------
DROP TABLE IF EXISTS `otc_prep_workorder`;
CREATE TABLE `otc_prep_workorder` (
  `id` bigint NOT NULL COMMENT '主键',
  `otc_workorder_id` bigint NOT NULL COMMENT '发货到c端工单id',
  `otc_workorder_detail_id` bigint NOT NULL COMMENT '发货到c端工单详情id',
  `qty` int NOT NULL COMMENT '数量',
  `prep_workorder_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '预工单状态',
  `prep_workorder_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '预工单类型',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otc_prep_picking_slip_id` bigint DEFAULT NULL COMMENT '发货到c端预拣货id',
  `putaway_qty` int NOT NULL COMMENT '上架数量',
  `inventory_reserve_id` bigint NOT NULL COMMENT '库存预定id',
  `order_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '订单类型',
  `pick_to_station` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '拣货到那里',
  `has_cus_ship_require` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有特定运输要求',
  `on_site_pack_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '现场包装标志',
  `bin_location_id` bigint DEFAULT NULL COMMENT '库位id',
  `locked_before` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '锁定前',
  `prep_workorder_version_int` int NOT NULL COMMENT '预工单产品版本',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `transaction_partner_id` bigint NOT NULL DEFAULT '0' COMMENT '供应商id',
  `prep_workorder_product_type` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Normal' COMMENT '产品类型',
  `process_type` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '流程类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_otc_prep_workorder_id` (`id`) USING BTREE,
  UNIQUE KEY `ix_otc_prep_workorder_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otc_prep_workorder_sub_picking_slip_id` (`otc_prep_picking_slip_id`) USING BTREE,
  KEY `ix_otc_prep_workorder_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_prep_workorder_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otc_prep_workorder_workorder_detail_id` (`otc_workorder_detail_id`) USING BTREE,
  KEY `ix_otc_prep_workorder_workorder_id` (`otc_workorder_id`) USING BTREE,
  KEY `ix_otc_prep_workorder_reserve_inventory_id` (`inventory_reserve_id`) USING BTREE,
  KEY `ix_otc_prep_workorder_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_id` (`bin_location_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='OTC预提工单';

-- ----------------------------
-- Table structure for otc_prep_workorder_bin_location
-- ----------------------------
DROP TABLE IF EXISTS `otc_prep_workorder_bin_location`;
CREATE TABLE `otc_prep_workorder_bin_location` (
  `id` bigint NOT NULL COMMENT '主键',
  `otc_workorder_id` bigint NOT NULL COMMENT '工单id',
  `otc_workorder_detail_id` bigint NOT NULL COMMENT '工单详情id',
  `otc_prep_workorder_id` bigint NOT NULL COMMENT '预工单id',
  `otc_prep_workorder_detail_id` bigint NOT NULL COMMENT '预工单详情id',
  `otc_prep_picking_slip_id` bigint NOT NULL COMMENT '预拣货id',
  `otc_prep_picking_slip_detail_id` bigint NOT NULL COMMENT '预拣货详情id',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `bin_location_detail_locked_id` bigint NOT NULL COMMENT '库位锁id',
  `product_version_id` bigint NOT NULL COMMENT '产品版本id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_sub_picking_slip_detail_id` (`otc_prep_picking_slip_detail_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_sub_picking_slip_id` (`otc_prep_picking_slip_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_sub_workorder_detail_id` (`otc_prep_workorder_detail_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_sub_workorder_id` (`otc_prep_workorder_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_workorder_detail_id` (`otc_workorder_detail_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_workorder_id` (`otc_workorder_id`) USING BTREE,
  KEY `ix_otc_prep_wo_bl_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC预提工单仓储位置';

-- ----------------------------
-- Table structure for otc_prep_workorder_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_prep_workorder_detail`;
CREATE TABLE `otc_prep_workorder_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otc_prep_workorder_id` bigint NOT NULL COMMENT '发货到c端预工单id',
  `line_num` int NOT NULL COMMENT '行序号',
  `inventory_locked_id` bigint DEFAULT NULL COMMENT '库存锁定id',
  `picked_qty` int NOT NULL COMMENT '拣货数量',
  `putaway_qty` int NOT NULL COMMENT '上架数量',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `parent_id` bigint DEFAULT NULL COMMENT '父节点id',
  `prep_workorder_detail_type` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '预工单详情类型',
  `prep_workorder_detail_version_int` int DEFAULT NULL COMMENT '预工单详情产品版本',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_prep_workorder_detail_header_id` (`otc_prep_workorder_id`) USING BTREE,
  KEY `ix_otc_prep_workorder_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_prep_workorder_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otc_prep_workorder_detail_locked_inventory_id` (`inventory_locked_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC预提工单详情';

-- ----------------------------
-- Table structure for otc_putaway_slip
-- ----------------------------
DROP TABLE IF EXISTS `otc_putaway_slip`;
CREATE TABLE `otc_putaway_slip` (
  `id` bigint NOT NULL COMMENT '主键',
  `workorder_id` bigint NOT NULL COMMENT '工单id',
  `picking_slip_id` bigint NOT NULL COMMENT '拣货单id',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `ref_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
  `putaway_slip_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'New' COMMENT '状态',
  `putaway_slip_type` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '上架类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `udx_ref_num` (`ref_num`) USING BTREE COMMENT '编码唯一',
  KEY `idx_putaway_slip` (`tenant_id`,`warehouse_id`,`workorder_id`) USING BTREE COMMENT '上架单索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC上架单';

-- ----------------------------
-- Table structure for otc_putaway_slip_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_putaway_slip_detail`;
CREATE TABLE `otc_putaway_slip_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `workorder_bin_location_id` bigint NOT NULL COMMENT '工单分配仓储id',
  `workorder_detail_id` bigint NOT NULL COMMENT '工单详情id',
  `workorder_id` bigint NOT NULL COMMENT '工单id',
  `picking_slip_id` bigint NOT NULL COMMENT '拣货单详情id',
  `picking_slip_detail_id` bigint NOT NULL COMMENT '拣货单id',
  `source_bin_location_id` bigint NOT NULL COMMENT '库位id',
  `source_bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `dest_bin_location_id` bigint NOT NULL COMMENT '预计库位id',
  `dest_bin_location_detail_id` bigint NOT NULL COMMENT '预计库位详情id',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `qty` int NOT NULL COMMENT '数量',
  `putaway_qty` int NOT NULL DEFAULT '0' COMMENT '上架数量',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `putaway_slip_id` bigint NOT NULL COMMENT '上架单id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_putaway_slip` (`tenant_id`,`warehouse_id`,`workorder_id`) USING BTREE COMMENT '上架单索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC上架单详情';

-- ----------------------------
-- Table structure for otc_request
-- ----------------------------
DROP TABLE IF EXISTS `otc_request`;
CREATE TABLE `otc_request` (
  `id` bigint NOT NULL COMMENT '主键',
  `request_ref_num` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '外部唯一标识码',
  `ship_express_flag` tinyint(1) NOT NULL COMMENT '是否快递标志',
  `ship_method` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输方式',
  `ship_carrier` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输公司',
  `ship_api_profile_ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递Api配置RefNum',
  `last_ship_date` timestamp NULL DEFAULT NULL COMMENT '最后发货日期',
  `insurance_amount_amount` decimal(18,3) DEFAULT NULL COMMENT '保险金额',
  `signature_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '签名类型',
  `ship_to_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '收货地址名称',
  `ship_to_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址公司',
  `ship_to_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收货地址国家',
  `ship_to_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收货地址州/省',
  `ship_to_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收货地址城市',
  `ship_to_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮编',
  `ship_to_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '收货地址1',
  `ship_to_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址2',
  `ship_to_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址3',
  `ship_to_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址邮箱',
  `ship_to_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址电话',
  `ship_to_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收货地址备注',
  `order_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单类型',
  `has_cus_ship_require` tinyint(1) NOT NULL COMMENT '是否有客户运输要求',
  `provide_shipping_label_flag` tinyint(1) NOT NULL COMMENT '是否提供运输标签',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴ID',
  `otc_request_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求状态',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `channel` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道',
  `ship_to_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '收货地址是否为住宅',
  `insurance_amount_currency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保险金额支付方式',
  `ship_from_address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址1',
  `ship_from_address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址2',
  `ship_from_address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址3',
  `ship_from_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址城市',
  `ship_from_address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址公司',
  `ship_from_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址国家',
  `ship_from_address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址邮箱',
  `ship_from_address_is_residential` tinyint(1) DEFAULT NULL COMMENT '发货地址是否为住宅',
  `ship_from_address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址名称',
  `ship_from_address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址备注',
  `ship_from_address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址电话',
  `ship_from_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址州/省',
  `ship_from_address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货地址邮编',
  `locked_before` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '锁定时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otc_request_ref_num` (`ref_num`) USING BTREE COMMENT '参考编号索引',
  KEY `ix_otc_request_warehouse_id` (`warehouse_id`) USING BTREE COMMENT '仓库ID索引',
  KEY `ix_otc_request_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE COMMENT '租户ID和仓库ID索引',
  KEY `ix_otc_request_transaction_partner_id_request_ref_num` (`transaction_partner_id`,`request_ref_num`),
  KEY `ix_otc_request_tenant_id_transaction_partner_id` (`tenant_id`,`transaction_partner_id`) COMMENT '租户ID和交易伙伴ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC请求';

-- ----------------------------
-- Table structure for otc_request_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_request_detail`;
CREATE TABLE `otc_request_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `otc_request_id` bigint NOT NULL COMMENT 'OTC请求ID',
  `qty` int NOT NULL COMMENT '数量',
  `finish_qty` int NOT NULL DEFAULT '0',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `line_num` int NOT NULL COMMENT '行序号',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_request_detail_header_id` (`otc_request_id`) USING BTREE COMMENT 'OTC请求ID索引',
  KEY `ix_otc_request_detail_product_id` (`product_id`) USING BTREE COMMENT '最新产品ID索引',
  KEY `ix_otc_request_detail_warehouse_id` (`warehouse_id`) USING BTREE COMMENT '仓库ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC请求详情';

-- ----------------------------
-- Table structure for otc_request_package
-- ----------------------------
DROP TABLE IF EXISTS `otc_request_package`;
CREATE TABLE `otc_request_package` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `tracking_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递号',
  `ship_express_flag` tinyint(1) NOT NULL COMMENT '是否快递标志',
  `ship_method` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输方式',
  `ship_carrier` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输公司',
  `ship_size_length` decimal(18,3) NOT NULL COMMENT '运输箱子-长',
  `ship_size_width` decimal(18,3) NOT NULL COMMENT '运输箱子-宽',
  `ship_size_height` decimal(18,3) NOT NULL COMMENT '运输箱子-高',
  `ship_size_weight` decimal(18,3) NOT NULL COMMENT '运输箱子-重量',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `otc_request_id` bigint NOT NULL COMMENT 'OTC请求ID',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ship_size_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'IN' COMMENT '运输箱子-长度单位',
  `ship_size_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'LB' COMMENT '运输箱子-重量单位',
  `package_multibox_upc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '多箱UPC',
  `package_multibox_line_num` int DEFAULT NULL COMMENT '多箱行号',
  `package_multibox_product_id` bigint DEFAULT NULL COMMENT '多箱产品最新ID',
  `package_multibox_version_int` int DEFAULT NULL COMMENT '多箱版本号',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_request_package_out_bound_to_crequest_id` (`otc_request_id`) USING BTREE,
  KEY `ix_otc_request_package_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC请求包裹';

-- ----------------------------
-- Table structure for otc_request_package_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_request_package_detail`;
CREATE TABLE `otc_request_package_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `otc_request_id` bigint NOT NULL COMMENT 'OTC请求ID',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otc_request_package_id` bigint NOT NULL COMMENT 'OTC请求包裹ID',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_request_package_detail_header_id` (`otc_request_package_id`) USING BTREE,
  KEY `ix_otc_request_package_detail_out_bound_to_crequest_id` (`otc_request_id`) USING BTREE,
  KEY `ix_otc_request_package_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_request_package_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC请求包裹详情';

-- ----------------------------
-- Table structure for otc_request_package_label
-- ----------------------------
DROP TABLE IF EXISTS `otc_request_package_label`;
CREATE TABLE `otc_request_package_label` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `label_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '面单类型',
  `label_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'label RefNum',
  `paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '纸张类型',
  `label_raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'label数据类型',
  `otc_request_id` bigint NOT NULL COMMENT 'OTC请求ID',
  `otc_request_package_id` bigint NOT NULL COMMENT 'OTC请求包裹ID',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `raw_data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据类型',
  `file_id_raw_data_type` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件系统数据类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  KEY `ix_otc_request_pl_out_bound_to_crequest_id` (`otc_request_id`) USING BTREE,
  KEY `ix_otc_request_pl_out_bound_to_crequest_package_id` (`otc_request_package_id`) USING BTREE,
  KEY `ix_otc_request_pl_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC请求包裹标签';

-- ----------------------------
-- Table structure for otc_ship_pallet
-- ----------------------------
DROP TABLE IF EXISTS `otc_ship_pallet`;
CREATE TABLE `otc_ship_pallet` (
  `id` bigint NOT NULL COMMENT '主键',
  `ship_carrier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输公司',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `carton_count` int NOT NULL DEFAULT '0' COMMENT '纸箱数量',
  `label_raw_data` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '原始托盘标签数据',
  `raw_data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '原始托盘标签类型',
  `label_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '托盘标签物流追踪码',
  `file_id_raw_data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '托盘标签原始数据类型文件id',
  `paper_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '托盘标签纸张类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `label_type` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '面单类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otc_ship_pallet_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otc_ship_pallet_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_otc_ship_pallet_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC运输托盘';

-- ----------------------------
-- Table structure for otc_ship_pallet_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_ship_pallet_detail`;
CREATE TABLE `otc_ship_pallet_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `tracking_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递号',
  `otc_package_id` bigint DEFAULT NULL COMMENT '打包id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `otc_ship_pallet_id` bigint NOT NULL COMMENT '发货到c端托盘运输id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_ship_pallet_detail_tracking_num` (`tracking_num`) USING BTREE,
  KEY `ix_otc_ship_pallet_detail_header_id` (`otc_ship_pallet_id`) USING BTREE,
  KEY `ix_otc_ship_pallet_detail_package_id` (`otc_package_id`) USING BTREE,
  KEY `ix_otc_ship_pallet_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC运输托盘详情';

-- ----------------------------
-- Table structure for otc_ship_station_config
-- ----------------------------
DROP TABLE IF EXISTS `otc_ship_station_config`;
CREATE TABLE `otc_ship_station_config` (
  `id` bigint NOT NULL COMMENT '主键',
  `ship_method` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输方式',
  `ship_carrier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输公司',
  `ship_method_category` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `ship_station` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `warehouse_id` bigint DEFAULT NULL COMMENT '仓库id',
  `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='快递公司配置';

-- ----------------------------
-- Table structure for otc_workorder
-- ----------------------------
DROP TABLE IF EXISTS `otc_workorder`;
CREATE TABLE `otc_workorder` (
  `id` bigint NOT NULL COMMENT '主键',
  `otc_request_id` bigint NOT NULL COMMENT '发货到c端请求id',
  `otc_picking_slip_id` bigint DEFAULT NULL COMMENT '发货到c端拣货id',
  `request_snapshot_ship_express_flag` tinyint(1) NOT NULL COMMENT '请求快照快递运输标志',
  `ship_method` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输方式',
  `ship_carrier` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输公司',
  `request_snapshot_last_ship_date` timestamp NULL DEFAULT NULL COMMENT '请求快照最后运输时间',
  `request_snapshot_insurance_amount_amount` decimal(18,3) DEFAULT NULL COMMENT '请求快照保险金额',
  `request_snapshot_signature_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求快照签名类型',
  `request_snapshot_order_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求快照订单类型',
  `request_snapshot_has_cus_ship_require` tinyint(1) NOT NULL COMMENT '请求快照是否有特定运输要求',
  `request_snapshot_ship_method` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求快照运输方式',
  `request_snapshot_provide_shipping_label_flag` tinyint(1) NOT NULL COMMENT '请求快照提供运输标签标志',
  `otc_workorder_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发货到c端工单状态',
  `build_ship_package_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '构建运输打包类型',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `request_snapshot_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'request快照备注',
  `request_snapshot_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'request快照RefNum',
  `request_snapshot_request_ref_num` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求快照RequestRefnum',
  `request_snapshot_ship_carrier` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求快照运输公司',
  `request_snapshot_transaction_partner_id` bigint NOT NULL COMMENT 'request快照transctionParterId',
  `request_snapshot_channel` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'request快照渠道',
  `workorder_prep_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '预处理工单状态',
  `request_snapshot_insurance_amount_currency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求快照保险金货币',
  `pick_to_station` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拣货到那里',
  `workorder_prep_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '预处理工单类型',
  `locked_before` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '锁定前',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `order_type` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '订单类型',
  `request_snapshot_ship_api_profile_ref_num` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递Api配置RefNum',
  `workorder_product_type` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Normal' COMMENT '产品类型',
  `process_type` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Normal' COMMENT '流程类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_otc_workorder_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_otc_workorder_out_bound_to_cpicking_slip_id` (`otc_picking_slip_id`) USING BTREE,
  KEY `ix_otc_workorder_out_bound_to_crequest_id` (`otc_request_id`) USING BTREE,
  KEY `ix_otc_workorder_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otc_workorder_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC工单';

-- ----------------------------
-- Table structure for otc_workorder_bin_location
-- ----------------------------
DROP TABLE IF EXISTS `otc_workorder_bin_location`;
CREATE TABLE `otc_workorder_bin_location` (
  `id` bigint NOT NULL COMMENT '主键',
  `otc_workorder_id` bigint NOT NULL COMMENT '发货到c端工单id',
  `otc_workorder_detail_id` bigint NOT NULL COMMENT '发货到c端工单详情id',
  `otc_picking_slip_id` bigint NOT NULL COMMENT '发货到c端拣货id',
  `otc_picking_slip_detail_id` bigint NOT NULL COMMENT '发货到c端拣货详情id',
  `bin_location_detail_id` bigint NOT NULL COMMENT '库位详情id',
  `bin_location_id` bigint NOT NULL COMMENT '库位id',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `product_version_id` bigint NOT NULL COMMENT '产品版本id',
  `bin_location_detail_locked_id` bigint DEFAULT NULL COMMENT 'ReadyToGo库位详情锁id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_workorder_bl_bin_location_detail_id` (`bin_location_detail_id`) USING BTREE,
  KEY `ix_otc_workorder_bl_bin_location_id` (`bin_location_id`) USING BTREE,
  KEY `ix_otc_workorder_bl_out_bound_to_cpicking_slip_detail_id` (`otc_picking_slip_detail_id`) USING BTREE,
  KEY `ix_otc_workorder_bl_out_bound_to_cpicking_slip_id` (`otc_picking_slip_id`) USING BTREE,
  KEY `ix_otc_workorder_bl_out_bound_to_cworkorder_detail_id` (`otc_workorder_detail_id`) USING BTREE,
  KEY `ix_otc_workorder_bl_out_bound_to_cworkorder_id` (`otc_workorder_id`) USING BTREE,
  KEY `ix_otc_workorder_bl_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_workorder_bl_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otc_workorder_bl_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='OTC工单仓储位置';

-- ----------------------------
-- Table structure for otc_workorder_detail
-- ----------------------------
DROP TABLE IF EXISTS `otc_workorder_detail`;
CREATE TABLE `otc_workorder_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `inventory_locked_id` bigint NOT NULL COMMENT '库存锁定id',
  `packed_qty` int NOT NULL COMMENT '打包为Package 数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `otc_workorder_id` bigint NOT NULL COMMENT '发货到c端工单id',
  `line_num` int NOT NULL COMMENT '行序号',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `qty` int NOT NULL COMMENT '数量',
  `finish_qty` int NOT NULL,
  `picked_qty` int NOT NULL COMMENT '拣货数量',
  `ready_to_ship_qty` int NOT NULL,
  `inventory_reserve_id` bigint DEFAULT NULL COMMENT '库存预定id',
  `reserve_qty` int NOT NULL COMMENT '预定数量',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `finish_reserve_qty` int NOT NULL DEFAULT '0' COMMENT '完成预定数量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_otc_workorder_detail_header_id` (`otc_workorder_id`) USING BTREE,
  KEY `ix_otc_workorder_detail_locked_inventory_id` (`inventory_locked_id`) USING BTREE,
  KEY `ix_otc_workorder_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_otc_workorder_detail_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_otc_workorder_detail_reserve_inventory_id` (`inventory_reserve_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='OTC工单详情';

-- ----------------------------
-- Table structure for pallet_empty_profile
-- ----------------------------
DROP TABLE IF EXISTS `pallet_empty_profile`;
CREATE TABLE `pallet_empty_profile` (
  `id` bigint NOT NULL COMMENT '主键',
  `name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名字',
  `pallet_size_length` decimal(18,3) NOT NULL COMMENT '托盘-长',
  `pallet_size_width` decimal(18,3) NOT NULL COMMENT '托盘-宽',
  `pallet_size_height` decimal(18,3) NOT NULL COMMENT '托盘-高',
  `pallet_size_weight` decimal(18,3) NOT NULL COMMENT '托盘-重量',
  `pallet_size_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '托盘-重量单位',
  `pallet_size_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '托盘-长度单位',
  `active_flag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `in_use_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '使用标识',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_pallet_empty_profile_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_pallet_empty_profile_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_pallet_empty_profile_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='托盘信息';

-- ----------------------------
-- Table structure for pallet_template
-- ----------------------------
DROP TABLE IF EXISTS `pallet_template`;
CREATE TABLE `pallet_template` (
  `id` bigint NOT NULL COMMENT '主键',
  `carton_per_layer` int NOT NULL COMMENT '每层几个箱子',
  `layers_count` int NOT NULL COMMENT '一共多少层',
  `ext_carton` int NOT NULL COMMENT '多了几个箱子',
  `pcs_per_carton` int NOT NULL COMMENT '每箱几个产品',
  `pallet_empty_profile_id` bigint DEFAULT NULL COMMENT '空托盘id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `set_default_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否设置默认',
  `product_version_id` bigint NOT NULL COMMENT '产品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_pallet_template_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_pallet_template_product_id` (`product_id`) USING BTREE,
  KEY `ix_pallet_template_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_pallet_template_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_pallet_template_product_version_id` (`product_version_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='打托模板';

-- ----------------------------
-- Table structure for prep_request
-- ----------------------------
DROP TABLE IF EXISTS `prep_request`;
CREATE TABLE `prep_request` (
  `id` bigint NOT NULL COMMENT '主键',
  `prep_request_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '请求状态',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易合作伙伴ID',
  `request_ref_num` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '外部唯一标识码',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_prep_request_ref_num` (`ref_num`) USING BTREE,
  KEY `ix_prep_request_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE,
  KEY `ix_prep_request_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='预请求';

-- ----------------------------
-- Table structure for prep_request_detail
-- ----------------------------
DROP TABLE IF EXISTS `prep_request_detail`;
CREATE TABLE `prep_request_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `prep_request_id` bigint NOT NULL COMMENT '准备请求ID',
  `prep_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '准备类型',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `line_num` int NOT NULL COMMENT '行序号',
  `qty` int NOT NULL COMMENT '数量',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_prep_request_detail_header_id` (`prep_request_id`) USING BTREE,
  KEY `ix_prep_request_detail_product_id` (`product_id`) USING BTREE,
  KEY `ix_prep_request_detail_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='预请求详情';

-- ----------------------------
-- Table structure for printer_settings
-- ----------------------------
DROP TABLE IF EXISTS `printer_settings`;
CREATE TABLE `printer_settings` (
  `id` bigint NOT NULL COMMENT '主键',
  `label_printer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标签打印机',
  `one_by_three_label_printer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '1x3标签打印机',
  `regular_printer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '常规打印机',
  `mobile_label_printer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '移动标签打印机',
  `mobile_c_lodop_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '移动CLodop打印插件',
  `scanner_plugin_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '扫描插件IP',
  `scanner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '扫描仪',
  `create_by` bigint NOT NULL,
  `create_time` timestamp NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint NOT NULL,
  `remove_flag` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='打印配置';

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
  `id` bigint NOT NULL COMMENT '主键',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴ID',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `supplier_sku` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '供应商SKU',
  `upc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'UPC码',
  `assembly_product_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '组装产品标志',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '标题',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
  `prod_type` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '危险品类型',
  `multibox_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '多箱标志',
  `group_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'None' COMMENT '组类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_ref_num` (`ref_num`) USING BTREE COMMENT 'refNum唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='产品';

-- ----------------------------
-- Table structure for product_component
-- ----------------------------
DROP TABLE IF EXISTS `product_component`;
CREATE TABLE `product_component` (
  `id` bigint NOT NULL COMMENT '主键',
  `assembly_product_id` bigint NOT NULL COMMENT '组装产品ID',
  `component_product_id` bigint NOT NULL COMMENT '组件产品ID',
  `assembly_instruction_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '组装说明备注',
  `component_qty` int NOT NULL,
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `component_version_int` int NOT NULL DEFAULT '0' COMMENT '组装产品版本号',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_product_component_assembly_product_id` (`assembly_product_id`) USING BTREE,
  KEY `ix_product_component_component_product_id` (`component_product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='产品组装';

-- ----------------------------
-- Table structure for product_component_copy1
-- ----------------------------
DROP TABLE IF EXISTS `product_component_copy1`;
CREATE TABLE `product_component_copy1` (
  `id` bigint NOT NULL COMMENT '主键',
  `assembly_product_id` bigint NOT NULL COMMENT '组装产品ID',
  `component_product_id` bigint NOT NULL COMMENT '组件产品ID',
  `assembly_instruction_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '组装说明备注',
  `component_qty` int NOT NULL,
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `component_version_int` int NOT NULL DEFAULT '0' COMMENT '组装产品版本号',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_product_component_assembly_product_id` (`assembly_product_id`) USING BTREE,
  KEY `ix_product_component_component_product_id` (`component_product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='产品组装';

-- ----------------------------
-- Table structure for product_group
-- ----------------------------
DROP TABLE IF EXISTS `product_group`;
CREATE TABLE `product_group` (
  `id` bigint NOT NULL COMMENT '主键',
  `parent_product_id` bigint NOT NULL COMMENT '父产品最新ID',
  `child_product_id` bigint NOT NULL COMMENT '子产品最新ID',
  `instruction_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '指令备注',
  `revert_instruction_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '回滚指令备注',
  `convert_group_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '转换组类型',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
  `group_version_int` int NOT NULL DEFAULT '0' COMMENT '同类产品版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  KEY `ix_product_group_parent_product_id` (`parent_product_id`) USING BTREE,
  KEY `ix_product_group_child_product_id` (`child_product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='产品同类';

-- ----------------------------
-- Table structure for product_hazmat
-- ----------------------------
DROP TABLE IF EXISTS `product_hazmat`;
CREATE TABLE `product_hazmat` (
  `id` bigint NOT NULL,
  `version_ref_num` bigint DEFAULT NULL,
  `package_instruction` varchar(255) DEFAULT NULL,
  `transportation_regulatory_class` varchar(255) DEFAULT NULL,
  `un_regulatory_id` bigint DEFAULT NULL,
  `tenant_id` bigint NOT NULL,
  `product_id` bigint DEFAULT NULL,
  `version` bigint NOT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for product_multibox
-- ----------------------------
DROP TABLE IF EXISTS `product_multibox`;
CREATE TABLE `product_multibox` (
  `id` bigint NOT NULL COMMENT '主键',
  `transaction_partner_id` bigint NOT NULL COMMENT '供应商id',
  `ship_length` decimal(18,3) NOT NULL COMMENT '发货长度',
  `ship_width` decimal(18,3) NOT NULL COMMENT '发货宽度',
  `ship_height` decimal(18,3) NOT NULL COMMENT '发货高度',
  `ship_weight` decimal(18,3) NOT NULL COMMENT '发货重量',
  `ship_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'LB' COMMENT '发货重量单位',
  `ship_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'IN' COMMENT '发货尺寸单位',
  `upc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'UPC码',
  `line_num` int NOT NULL COMMENT '行序号',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
  `product_id` bigint NOT NULL COMMENT '需要装多箱的产品id',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `multibox_version_int` int NOT NULL DEFAULT '0' COMMENT '多箱产品版本号',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  KEY `ix_product_multibox_product_id` (`product_id`) USING BTREE,
  KEY `ix_product_multibox_id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='产品多箱';

-- ----------------------------
-- Table structure for product_multibox_detail
-- ----------------------------
DROP TABLE IF EXISTS `product_multibox_detail`;
CREATE TABLE `product_multibox_detail` (
  `id` bigint NOT NULL COMMENT '主键',
  `line_num` int NOT NULL COMMENT '行序号',
  `qty` int NOT NULL COMMENT '数量',
  `product_multibox_id` bigint NOT NULL COMMENT '产品最新多箱ID',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
  `product_id` bigint NOT NULL COMMENT '多箱里装配件产品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_product_multibox_detail_header_id` (`product_multibox_id`) USING BTREE,
  KEY `ix_product_multibox_detail_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='产品多箱详情';

-- ----------------------------
-- Table structure for product_scan
-- ----------------------------
DROP TABLE IF EXISTS `product_scan`;
CREATE TABLE `product_scan` (
  `id` bigint NOT NULL COMMENT '主键',
  `ref_id` bigint NOT NULL COMMENT '关联id（为多箱时为多箱id否则为产品id）',
  `scan_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '扫描编号',
  `product_id` bigint NOT NULL COMMENT '产品id',
  `transaction_partner_id` bigint NOT NULL COMMENT '供应商id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `product_type` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '产品类型',
  `product_attribute` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品属性（是产品，还是多箱）',
  `line_num` int DEFAULT NULL COMMENT '多箱时展示多箱的行号',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
  `default_flag` tinyint(1) NOT NULL COMMENT '是否是系统默认生成的（默认的无法删除）',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_product_scan_product_id` (`product_id`) USING BTREE,
  KEY `ix_product_scan_scan_num` (`ref_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='产品扫描';

-- ----------------------------
-- Table structure for product_version
-- ----------------------------
DROP TABLE IF EXISTS `product_version`;
CREATE TABLE `product_version` (
  `id` bigint NOT NULL COMMENT '主键',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `supplier_sku` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '供应商SKU',
  `ref_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `upc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'UPC码',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品标题',
  `net_length` decimal(18,3) NOT NULL COMMENT '净长度',
  `net_width` decimal(18,3) NOT NULL COMMENT '净宽度',
  `net_height` decimal(18,3) NOT NULL COMMENT '净高度',
  `net_weight` decimal(18,3) NOT NULL COMMENT '净重量',
  `ship_length` decimal(18,3) NOT NULL COMMENT '发货长度',
  `ship_width` decimal(18,3) NOT NULL COMMENT '发货宽度',
  `ship_height` decimal(18,3) NOT NULL COMMENT '发货高度',
  `ship_weight` decimal(18,3) NOT NULL COMMENT '发货重量',
  `carton_length` decimal(18,3) NOT NULL COMMENT '纸箱长度',
  `carton_width` decimal(18,3) NOT NULL COMMENT '纸箱宽度',
  `carton_height` decimal(18,3) NOT NULL COMMENT '纸箱高度',
  `carton_weight` decimal(18,3) NOT NULL COMMENT '纸箱重量',
  `pcs_per_carton` int NOT NULL COMMENT '每箱数量',
  `product_version_int` int NOT NULL COMMENT '产品版本号',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
  `carton_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'IN' COMMENT '纸箱尺寸单位',
  `carton_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'LB' COMMENT '纸箱重量单位',
  `net_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'IN' COMMENT '净尺寸单位',
  `net_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'LB' COMMENT '净重量单位',
  `ship_dimension_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'IN' COMMENT '发货尺寸单位',
  `ship_weight_unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'LB' COMMENT '发货重量单位',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴ID',
  `product_remeasure_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'None' COMMENT '产品重新测量类型',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_product_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='产品版本详情';

-- ----------------------------
-- Table structure for profile_partner
-- ----------------------------
DROP TABLE IF EXISTS `profile_partner`;
CREATE TABLE `profile_partner` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `version` bigint NOT NULL COMMENT '版本时间戳',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除备注',
  `service_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `category_code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分类代码',
  `category_desc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分类描述',
  `value_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值类型',
  `name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `value` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `sort` int NOT NULL COMMENT '排序',
  `active_flag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '激活标志',
  `code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '代码',
  `create_by` bigint NOT NULL COMMENT '创建者ID',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者ID',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '删除标志',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='patner配置';

-- ----------------------------
-- Table structure for profile_system
-- ----------------------------
DROP TABLE IF EXISTS `profile_system`;
CREATE TABLE `profile_system` (
  `id` bigint NOT NULL,
  `version` bigint NOT NULL,
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `service_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `category_code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `category_desc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `value_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `value` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `sort` int NOT NULL,
  `active_flag` tinyint(1) NOT NULL DEFAULT '1',
  `code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `create_by` bigint NOT NULL,
  `create_time` timestamp NOT NULL,
  `update_by` bigint DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `remove_flag` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='系统保底配置';

-- ----------------------------
-- Table structure for profile_warehouse
-- ----------------------------
DROP TABLE IF EXISTS `profile_warehouse`;
CREATE TABLE `profile_warehouse` (
  `id` bigint NOT NULL COMMENT '主键',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `service_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型',
  `category_code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分类代码',
  `category_desc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分类描述',
  `value_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值类型',
  `name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `value` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值',
  `note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `sort` int NOT NULL COMMENT '排序',
  `active_flag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '激活标志',
  `code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '代码',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_profile_warehouse_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库配置';

-- ----------------------------
-- Table structure for quote
-- ----------------------------
DROP TABLE IF EXISTS `quote`;
CREATE TABLE `quote` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `active_flag` tinyint(1) NOT NULL COMMENT '是否有效(0-无效，1-有效)',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `name` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_quote_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库报价';

-- ----------------------------
-- Table structure for sequence
-- ----------------------------
DROP TABLE IF EXISTS `sequence`;
CREATE TABLE `sequence` (
  `id` bigint NOT NULL COMMENT '主键',
  `sequence_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '序列类型',
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '序列代码',
  `sequence_id` bigint NOT NULL COMMENT '序列ID',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ix_sequence_code` (`code`) USING BTREE,
  UNIQUE KEY `ix_sequence_sequence_type` (`sequence_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='全局序列号ref';

-- ----------------------------
-- Table structure for supplier
-- ----------------------------
DROP TABLE IF EXISTS `supplier`;
CREATE TABLE `supplier` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `transaction_partner_id` bigint NOT NULL COMMENT '交易伙伴id',
  `active_flag` tinyint(1) NOT NULL COMMENT '是否有效(0-无效，1-有效)',
  PRIMARY KEY (`id`),
  KEY `ix_supplier_tenant_id_transaction_partner_id` (`tenant_id`,`transaction_partner_id`) COMMENT '租户ID和交易伙伴id索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='供应商信息';

-- ----------------------------
-- Table structure for supplier_quote
-- ----------------------------
DROP TABLE IF EXISTS `supplier_quote`;
CREATE TABLE `supplier_quote` (
  `id` bigint NOT NULL COMMENT '主键',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记(0-有效，1-已删除)',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `note` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `active_flag` tinyint(1) NOT NULL COMMENT '是否有效(0-无效，1-有效)',
  `name` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `ref_num` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `supplier_id` bigint NOT NULL COMMENT '供应商id',
  `quote_id` bigint NOT NULL COMMENT '仓库报价',
  `start_time` timestamp NOT NULL COMMENT '有效开始时间',
  `end_time` timestamp NOT NULL COMMENT '有效结束时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_inbound_request_ref_num` (`ref_num`) COMMENT '参考编号唯一索引',
  KEY `ix_supplier_quote_tenant_id_warehouse_id` (`tenant_id`,`warehouse_id`) COMMENT '租户ID和仓库ID组合索引',
  KEY `ix_supplier_quote_tenant_id_supplier_id` (`tenant_id`,`supplier_id`) COMMENT '租户ID和供应商ID组合索引',
  KEY `ix_supplier_quote_tenant_id_quote_id` (`tenant_id`,`quote_id`) COMMENT '租户ID和仓库报价ID组合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='供应商-仓库报价';

-- ----------------------------
-- Table structure for warehouse
-- ----------------------------
DROP TABLE IF EXISTS `warehouse`;
CREATE TABLE `warehouse` (
  `id` bigint NOT NULL COMMENT '主键',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '仓库编号',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '仓库名称',
  `time_zone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '时区',
  `address_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名字',
  `address_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司',
  `address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '国家',
  `address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '洲',
  `address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '城市',
  `address_zip_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮编',
  `address_addr1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '地址1',
  `address_addr2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '地址2',
  `address_addr3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '地址3',
  `address_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮箱',
  `address_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电话',
  `address_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `ref_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '唯一标识码',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `address_is_residential` tinyint(1) DEFAULT NULL COMMENT '是否是住宅地址',
  `amazon_ship_profile_ship_api_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `fedex_ship_profile_ship_api_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `usps_ship_profile_ship_api_ref_num` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `upsship_profile_ship_api_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `active_flag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效',
  `amazon_ship_pallet_profile_ship_api_ref_num` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `amazon_warehouse_code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '亚马逊仓库编码',
  `ssccprefix` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `default_otb_assembly_bin_location_id` bigint DEFAULT NULL COMMENT 'otb  assembly',
  `default_otb_convert_bin_location_id` bigint DEFAULT NULL COMMENT 'otb  convert',
  `default_otb_multibox_bin_location_id` bigint DEFAULT NULL COMMENT 'otb  multibox',
  `default_otc_readytogo_bin_location_id` bigint DEFAULT NULL COMMENT 'otc  readytogo',
  `default_otc_outside_bin_location_id` bigint DEFAULT NULL COMMENT 'otc  outside',
  `default_otb_readytogo_bin_location_id` bigint DEFAULT NULL COMMENT 'otb  readytogo',
  `default_otb_outside_bin_location_id` bigint DEFAULT NULL COMMENT 'otb  outside',
  `default_otc_convert_bin_location_id` bigint DEFAULT NULL COMMENT 'otc  convert',
  `default_otc_multibox_bin_location_id` bigint DEFAULT NULL COMMENT 'otc  multibox',
  `default_otc_assembly_bin_location_id` bigint DEFAULT NULL COMMENT 'otc  assembly',
  `amazon_ship_from_party_code` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货方信息',
  `amazon_vendor_container_code` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'vendorContainerId',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库基础信息';

-- ----------------------------
-- Table structure for warehouse_operation
-- ----------------------------
DROP TABLE IF EXISTS `warehouse_operation`;
CREATE TABLE `warehouse_operation` (
  `id` bigint NOT NULL COMMENT '主键',
  `operator_id` bigint NOT NULL COMMENT '操作人id',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_warehouse_operation_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='仓库分配';

-- ----------------------------
-- Table structure for warehouse_product
-- ----------------------------
DROP TABLE IF EXISTS `warehouse_product`;
CREATE TABLE `warehouse_product` (
  `id` bigint NOT NULL COMMENT '主键',
  `slap_and_go_flag` tinyint(1) DEFAULT NULL COMMENT '即发货标志',
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  `warehouse_id` bigint NOT NULL COMMENT '仓库id',
  `product_id` bigint NOT NULL COMMENT '产品新品id',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_warehouse_product_product_id` (`product_id`) USING BTREE,
  KEY `ix_warehouse_product_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `ix_warehouse_product_op_id_warehouse_id` (`tenant_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='产品即发货';

-- ----------------------------
-- Table structure for warehouse_sequence
-- ----------------------------
DROP TABLE IF EXISTS `warehouse_sequence`;
CREATE TABLE `warehouse_sequence` (
  `id` bigint NOT NULL COMMENT '主键',
  `warehouse_ref_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `sequence_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `to_day` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `sequence_id` bigint NOT NULL,
  `version` bigint NOT NULL COMMENT '乐观锁版本号',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `update_by` bigint NOT NULL COMMENT '最后更新人',
  `update_time` timestamp NOT NULL COMMENT '最后更新时间',
  `remove_flag` tinyint(1) NOT NULL COMMENT '逻辑删除标记，取值0/1，1表示被删除的数据',
  `deleted_note` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '删除原因',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='生成仓库唯一refNum';

-- ----------------------------
-- Procedure structure for insert_fee_config_inbound
-- ----------------------------
DROP PROCEDURE IF EXISTS `insert_fee_config_inbound`;
delimiter ;;
CREATE DEFINER=`frp`@`%` PROCEDURE `insert_fee_config_inbound`()
BEGIN
    DECLARE start_id BIGINT DEFAULT 1868588896309346307;
    DECLARE i INT DEFAULT 1;

    WHILE i <= 30 DO
        INSERT INTO frp_business_dev.fee_config_inbound (
            id,
            create_by,
            create_time,
            update_by,
            update_time,
            remove_flag,
            version,
            deleted_note,
            tenant_id,
            warehouse_id,
            note,
            active_flag,
            condition_type,
            currency,
            fee_calculation_type,
            extra_fee_judge_fields,
            name,
            ref_num,
            quote_id
        ) VALUES (
            start_id + i - 1,
            'admin', -- 假设create_by是一个用户名，而不是时间戳
            CURRENT_TIMESTAMP, -- 使用当前时间戳
            'admin', -- 假设update_by是一个用户名，而不是时间戳
            CURRENT_TIMESTAMP, -- 使用当前时间戳
            0,
            NULL,
            1868587667315359746,
            1868946949315883010,
            '',
            1,
            'Pcs',
            'USD',
            'Base',
            '',
            '111',
            'WH01C01G-FCI-2503240003',
            NULL
        );
        SET i = i + 1;
    END WHILE;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
