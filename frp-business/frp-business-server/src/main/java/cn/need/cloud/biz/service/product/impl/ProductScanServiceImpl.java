package cn.need.cloud.biz.service.product.impl;

import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductAttributeEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductLogStatusEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductScanTypeEnum;
import cn.need.cloud.biz.converter.product.ProductScanConverter;
import cn.need.cloud.biz.mapper.product.ProductScanMapper;
import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.entity.product.ProductScan;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.param.product.create.ProductScanCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductScanUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductScanQuery;
import cn.need.cloud.biz.model.query.product.ScanNumAndPartnerQuery;
import cn.need.cloud.biz.model.vo.product.BaseProductVersionInfoVO;
import cn.need.cloud.biz.model.vo.product.ProductScanVO;
import cn.need.cloud.biz.model.vo.product.page.ProductScanPageVO;
import cn.need.cloud.biz.service.helper.auditshowlog.product.ProductAuditLogHelper;
import cn.need.cloud.biz.service.product.ProductScanService;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品扫描 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class ProductScanServiceImpl extends SuperServiceImpl<ProductScanMapper, ProductScan> implements ProductScanService {

    @Resource
    private ProductVersionService productVersionService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(ProductScanCreateParam createParam) {
        // 检查传入产品扫描参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        ProductCache productCache = ProductCacheUtil.getById(createParam.getProductId());

        // 获取产品扫描转换器实例，用于将产品扫描参数对象转换为实体对象
        ProductScanConverter converter = Converters.get(ProductScanConverter.class);

        // 将产品扫描参数对象转换为实体对象并初始化
        ProductScan entity = initProductScan(converter.toEntity(createParam));
        // 设置产品扫描对象中的交易合作伙伴ID
        entity.setTransactionPartnerId(productCache.getTransactionPartnerId());
        // 新增的是产品本身
        entity.setRefId(createParam.getProductId());
        entity.setProductAttribute(ProductAttributeEnum.PRODUCT.getType());
        entity.setDefaultFlag(Boolean.FALSE);
        // 插入产品扫描实体对象到数据库
        super.insert(entity);

        // 记录到log日志
        ProductAuditLogHelper.recordLog(
                BeanUtil.copyNew(productCache, Product.class),
                ProductLogStatusEnum.ADD.getStatus(),
                BaseTypeLogEnum.SCAN.getType(),
                null,
                StringUtil.format("ScanNum:{},ProductType:{}", entity.getScanNum(), entity.getProductType())
        );
        // 返回产品扫描ID
        return entity.getId();
    }

    @Override
    public List<ProductScan> getProductMultiboxScans(Long productId) {
        return lambdaQuery().eq(ProductScan::getProductId, productId).eq(ProductScan::getProductAttribute, ProductAttributeEnum.MULTIBOX.getType()).list();
    }

    @Override
    public void deleteByProductId(Long productId) {
        lambdaUpdate().eq(ProductScan::getProductId, productId).set(ProductScan::getRemoveFlag, DataState.ENABLED).update();
    }

    @Override
    public ProductScanVO productByScanNumAndTransactionPartnerId(ScanNumAndPartnerQuery query) {
        //校验入参
        if (ObjectUtil.isEmpty(query.getScanNum())
                || ObjectUtil.isEmpty(query.getTransactionPartnerId())
        ) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "ScanNum or TransactionPartnerId"));
        }
        return getProductScanVO(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(ProductScanUpdateParam updateParam) {
        // 检查传入产品扫描参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取产品扫描转换器实例，用于将产品扫描参数对象转换为实体对象
        ProductScanConverter converter = Converters.get(ProductScanConverter.class);

        // 将产品扫描参数对象转换为实体对象
        ProductScan entity = converter.toEntity(updateParam);

        // 执行更新产品扫描操作
        return super.update(entity);

    }

    @Override
    public List<ProductScanPageVO> listByQuery(ProductScanQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<ProductScanPageVO> pageByQuery(PageSearch<ProductScanQuery> search) {
        Page<ProductScan> page = Conditions.page(search, entityClass);
        List<ProductScanPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public ProductScanVO detailById(Long id) {
        ProductScan entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in ProductScan");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "ProductScan", id));
        }
        return buildProductScanVO(entity);
    }

    @Override
    public void initInsertScanByProduct(Product entity) {
        List<ProductScan> productScans = new ArrayList<>();

        // 初始化并创建基于UPC的产品扫描对象
        if (ObjectUtil.isNotEmpty(entity.getUpc())) {
            productScans.add(initFillScanByProduct(entity, ProductScanTypeEnum.UPC));
        }

        // 初始化并创建基于SKU的产品扫描对象
        productScans.add(initFillScanByProduct(entity, ProductScanTypeEnum.SKU));

        // 初始化并创建基于参考编号的产品扫描对象
        productScans.add(initFillScanByProduct(entity, ProductScanTypeEnum.REF_NUM));

        // 将创建的三个产品扫描对象插入数据库
        super.insertBatch(productScans);
    }

    @Override
    public void insertOrUpdateByMultibox(Set<ProductMultibox> newList, Long productId) {
        List<ProductScan> oldList = getProductMultiboxScans(productId);
        //获取lineNum映射
        Map<Integer, ProductScan> oldMap = oldList.stream().collect(Collectors.toMap(ProductScan::getLineNum, o -> o));
        // 将新列表转换为映射，其中键为行号，值为根据ProductMultibox对象创建的ProductScan对象
        Map<Integer, ProductScan> newMap = newList.stream().collect(Collectors.toMap(ProductMultibox::getLineNum,
                o -> {
                    //根据产品多箱信息 对应建产品扫描信息
                    ProductScan productScan = new ProductScan();
                    productScan.setProductId(productId);
                    productScan.setRefId(o.getId());
                    productScan.setScanNum(o.getUpc());
                    productScan.setProductAttribute(ProductAttributeEnum.MULTIBOX.getType());
                    productScan.setTransactionPartnerId(o.getTransactionPartnerId());
                    productScan.setProductType(ProductScanTypeEnum.UPC.getType());
                    productScan.setDefaultFlag(Boolean.TRUE);
                    productScan.setLineNum(o.getLineNum());
                    return productScan;
                }));

        // 获取旧映射的行号集合
        Set<Integer> oldLineList = oldMap.keySet();
        // 获取新映射的行号集合
        Set<Integer> newLineList = newMap.keySet();
        // 需要更新的数据的lineNum
        Set<Integer> intersection = new HashSet<>(oldLineList);
        intersection.retainAll(newLineList);
        // 如果有需要更新的数据
        if (ObjectUtil.isNotEmpty(intersection)) {
            // 创建一个更新列表，用于批量更新产品扫描信息
            List<ProductScan> updateList = newMap.entrySet().stream()
                    .filter(o -> intersection.contains(o.getKey()))
                    .map(o -> {
                        // 获取新产品扫描信息
                        ProductScan value = o.getValue();
                        // 设置旧的产品扫描信息的ID
                        value.setId(oldMap.get(o.getKey()).getId());
                        // 返回更新后的对象
                        return value;
                    }).toList();
            // 调用父类的方法进行批量更新
            super.updateBatch(updateList);
        }
        // 需要删除的数据的lineNum
        Set<Integer> delList = new HashSet<>(oldLineList);
        delList.removeAll(newLineList);
        // 如果有需要删除的数据
        if (ObjectUtil.isNotEmpty(delList)) {
            // 获取需要删除的数据的ID列表
            List<Long> idList = oldMap.entrySet().stream().filter(o -> delList.contains(o.getKey())).map(o -> o.getValue().getId()).toList();
            // 批量删除
            super.removeByIds(idList);
        }

        // 需要新增的数据的lineNum
        Set<Integer> addList = new HashSet<>(newLineList);
        addList.removeAll(oldLineList);
        // 如果有需要新增的数据
        if (ObjectUtil.isNotEmpty(addList)) {
            // 创建一个新增列表，用于批量插入产品扫描信息
            List<ProductScan> list = newMap.entrySet().stream().filter(o -> addList.contains(o.getKey())).map(Map.Entry::getValue).toList();
            // 调用父类的方法进行批量插入
            super.insertBatch(list);
        }
    }

    @Override
    public void deleteByMultiboxIds(List<Long> idList) {
        lambdaUpdate().in(ProductScan::getRefId, idList).set(ProductScan::getRemoveFlag, DataState.ENABLED).update();
    }

    @Override
    public List<ProductScanVO> productByScanNum(ScanNumAndPartnerQuery query) {
        //sql查询
        List<ProductScan> list = getListByScanNumAndTransactionPartnerId(query);

        //校验出参
        if (ObjectUtil.isEmpty(list)) {
            throw new BusinessException(StringUtil.format("ScanNum:{} can not find Product", query.getScanNum()));
        }

        //转vo对象
        return BeanUtil.copyNew(list, ProductScanVO.class);
    }

    @Override
    public List<BaseProductVersionInfoVO> productVersionByScanNum(ScanNumAndPartnerQuery query) {
        //sql查询
        List<ProductScan> list = getListByScanNumAndTransactionPartnerId(query);

        //校验出参
        if (ObjectUtil.isEmpty(list)) {
            throw new BusinessException(StringUtil.format("ScanNum:{} can not find Product", query.getScanNum()));
        }

        //获取产品id
        Set<Long> productIdList = list
                .stream()
                .map(ProductScan::getProductId)
                .collect(Collectors.toSet());

        //填充产品版本信息
        List<ProductVersion> productVersionList = productVersionService.getProductVersionIds(productIdList);
        //返回产品版本信息
        return productVersionList
                .stream()
                .distinct()
                .map(item -> new BaseProductVersionInfoVO(item.getId()))
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateScanDataForProduct(Long productId, String oldSupplierSku, String newSupplierSku, String oldUpc, String newUpc) {
        if (ObjectUtil.isEmpty(productId)) {
            return;
        }

        // 获取该产品的所有扫描记录
        List<ProductScan> productScans = lambdaQuery()
                .eq(ProductScan::getProductId, productId)
                .eq(ProductScan::getProductAttribute, ProductAttributeEnum.PRODUCT.getType())
                .eq(ProductScan::getDefaultFlag, Boolean.TRUE)
                .list();

        if (ObjectUtil.isEmpty(productScans)) {
            return;
        }

        List<ProductScan> updateList = new ArrayList<>();

        for (ProductScan productScan : productScans) {
            boolean needUpdate = false;

            // 更新SKU类型的扫描记录
            if (ProductScanTypeEnum.SKU.getType().equals(productScan.getProductType())
                && ObjectUtil.isNotEmpty(newSupplierSku)
                && !StringUtil.equals(productScan.getScanNum(), newSupplierSku)) {
                productScan.setScanNum(newSupplierSku);
                needUpdate = true;
            }

            // 更新UPC类型的扫描记录
            if (ProductScanTypeEnum.UPC.getType().equals(productScan.getProductType())
                && ObjectUtil.isNotEmpty(newUpc)
                && !StringUtil.equals(productScan.getScanNum(), newUpc)) {
                productScan.setScanNum(newUpc);
                needUpdate = true;
            }

            if (needUpdate) {
                updateList.add(productScan);
            }
        }

        // 批量更新扫描记录
        if (ObjectUtil.isNotEmpty(updateList)) {
            super.updateBatch(updateList);
        }
    }

    /**
     * 根据扫描码和交易伙伴ID获取产品扫描对象
     *
     * @param query 查询参数
     * @return ProductScanVO
     */
    @Nullable
    private ProductScanVO getProductScanVO(ScanNumAndPartnerQuery query) {
        //sql查询
        List<ProductScan> list = getListByScanNumAndTransactionPartnerId(query);
        //校验出参
        if (ObjectUtil.isEmpty(list)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_WITH_CONDITION, "Product", "ScanNum"));
        }
        if (list.size() > 1) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "ScanNum is duplicated"));
        }
        // 根据列表中的第一个产品信息构建一个ProductScanVO对象
        ProductScanVO productScanVO = buildProductScanVO(list.get(NumberUtils.INTEGER_ZERO));

        // 使用产品缓存工具类填充ProductScanVO对象的详细信息
        ProductCacheUtil.filledProduct(productScanVO);

        return productScanVO;
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 根据产品信息初始化填充扫描信息
     *
     * @param entity      产品实体，包含产品的基本信息
     * @param productType 产品类型枚举，指示产品的类型
     * @return 返回初始化后的ProductScan对象
     */
    private ProductScan initFillScanByProduct(Product entity, ProductScanTypeEnum productType) {
        // 初始填充产品扫描对象
        ProductScan productScan = new ProductScan();
        // 设置产品ID
        productScan.setProductId(entity.getId());
        // 设置参考ID
        productScan.setRefId(entity.getId());
        // 设置交易伙伴ID
        productScan.setTransactionPartnerId(entity.getTransactionPartnerId());
        // 设置产品属性为“产品”
        productScan.setProductAttribute(ProductAttributeEnum.PRODUCT.getType());
        // 设置默认标志为true
        productScan.setDefaultFlag(Boolean.TRUE);
        // 根据产品类型设置产品扫描信息
        switch (productType) {
            case UPC:
                // 如果是UPC类型，设置扫描号码为UPC编号，并设置产品类型为UPC类型
                productScan.setScanNum(entity.getUpc());
                productScan.setProductType(ProductScanTypeEnum.UPC.getType());
                break;
            case SKU:
                // 如果是SKU类型，设置扫描号码为供应商SKU编号，并设置产品类型为SKU类型
                productScan.setScanNum(entity.getSupplierSku());
                productScan.setProductType(ProductScanTypeEnum.SKU.getType());
                break;
            case REF_NUM:
                // 如果是参考号码类型，设置扫描号码为参考号码，并设置产品类型为参考号码类型
                productScan.setScanNum(entity.getRefNum());
                productScan.setProductType(ProductScanTypeEnum.REF_NUM.getType());
                break;
            default:
                // 如果是不支持的产品类型，抛出业务异常
                throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Unsupported product scan type"));
        }
        // 返回填充好的产品扫描对象
        return productScan;
    }

    /**
     * 初始化产品扫描对象
     * 此方法用于设置产品扫描对象的必要参数，确保其处于有效状态
     *
     * @param entity 产品扫描对象，不应为空
     * @return 返回初始化后的产品扫描
     * @throws BusinessException 如果传入的产品扫描为空，则抛出此异常
     */
    private ProductScan initProductScan(ProductScan entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 根据扫描号和交易合作伙伴ID查询产品扫描列表
     *
     * @param query 查询条件
     * @return List<ProductScan> 产品扫描列表
     */
    private List<ProductScan> getListByScanNumAndTransactionPartnerId(ScanNumAndPartnerQuery query) {
        return lambdaQuery()
                .eq(ProductScan::getScanNum, query.getScanNum())
                .eq(ObjectUtil.isNotEmpty(query.getTransactionPartnerId()), ProductScan::getTransactionPartnerId, query.getTransactionPartnerId())
                .list();
    }

    /**
     * 构建产品扫描VO对象
     *
     * @param entity 产品扫描对象
     * @return 返回包含详细信息的产品扫描VO对象
     */
    private ProductScanVO buildProductScanVO(ProductScan entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的产品扫描VO对象
        return Converters.get(ProductScanConverter.class).toVO(entity);
    }

}
