package cn.need.cloud.biz.model.vo.otb.pickingslip;

import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlip;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.query.otb.pickingslip.prep.OtbPrepPickingSlipProductPickQuery;
import lombok.Data;

import java.util.List;

/***
 * 拣货上下文信息
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Data
public class OtbPrepPickingSlipPickContextVO {

    /**
     * 拣货产品条件列表
     */
    private List<OtbPrepPickingSlipProductPickQuery> pickList;

    /**
     * 当前拣货单
     */
    private OtbPrepPickingSlip prepPickingSlip;

    /**
     * 拣货单详情 拣货后信息
     */
    private List<OtbPrepPickingSlipDetailPickVO> pickAfterPrepDetailList;


    /**
     * 关联Prep工单日志信息
     */
    private List<OtbPrepWorkorder> prepWorkorderList;
}
