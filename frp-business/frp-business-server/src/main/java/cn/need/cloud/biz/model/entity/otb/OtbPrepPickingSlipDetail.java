package cn.need.cloud.biz.model.entity.otb;

import cn.need.cloud.biz.model.entity.base.pickingslip.PrepPickingSlipDetailModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * <p>
 * otb预拣货单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName
public class OtbPrepPickingSlipDetail extends PrepPickingSlipDetailModel {


    @Serial
    private static final long serialVersionUID = 1825118789161836201L;

    /**
     * otb预拣货单id
     */
    @TableField("otb_prep_picking_slip_id")
    private Long otbPrepPickingSlipId;


    /**
     * 危险品版本号
     */
    @TableField("hazmat_version_ref_num")
    private String hazmatVersionRefNum;

}
