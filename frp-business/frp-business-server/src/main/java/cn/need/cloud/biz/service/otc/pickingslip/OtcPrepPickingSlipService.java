package cn.need.cloud.biz.service.otc.pickingslip;

import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlip;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipListQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipProductPickQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipVO;
import cn.need.cloud.biz.model.vo.page.OtcPrepPickingSlipPageVO;
import cn.need.cloud.biz.service.base.HeaderPrintedService;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * OTC预提货单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OtcPrepPickingSlipService extends SuperService<OtcPrepPickingSlip>,
        RefNumService<OtcPrepPickingSlip, OtcPrepPickingSlipService>,
        HeaderPrintedService<OtcPrepPickingSlip, OtcPrepPickingSlipService, PrintQuery> {

    /**
     * 根据查询条件获取OTC预提货单列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个OTC预提货单对象的列表(分页)
     */
    PageData<OtcPrepPickingSlipPageVO> pageByQuery(PageSearch<OtcPrepPickingSlipListQuery> search);

    /**
     * 根据ID获取OTC预提货单
     *
     * @param id OTC预提货单ID
     * @return 返回OTC预提货单VO对象
     */
    OtcPrepPickingSlipVO detailById(Long id);

    /**
     * 根据OTC预提货单唯一编码获取OTC预提货单
     *
     * @param refNum OTC预提货单唯一编码
     * @return 返回OTC预提货单VO对象
     */
    OtcPrepPickingSlipVO detailByRefNum(String refNum);

    /**
     * 拣货
     *
     * @param query         拣货条件
     * @param pickingSlipId 拣货单id
     * @return /
     */
    boolean pick(List<OtcPrepPickingSlipProductPickQuery> query, Long pickingSlipId);

    /**
     * OTC预拣货单单字段去重下拉
     *
     * @param query 列表查询条件
     * @return 下拉
     */
    List<DropProVO> distinctValue(OtcPrepPickingSlipQuery query);

    /**
     * 构建OTC预提货单VO
     *
     * @param prepPickingSlip 拣货单
     * @return /
     */
    OtcPrepPickingSlipVO buildOtcPrepPickingSlipVO(OtcPrepPickingSlip prepPickingSlip);
}