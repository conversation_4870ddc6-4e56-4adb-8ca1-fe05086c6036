package cn.need.cloud.biz.model.vo.feeconfig;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;


/**
 * 仓库报价费用配置storage详情 VO对象
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库报价费用配置storage详情 VO对象")
public class FeeConfigStorageDetailVO extends BaseSuperVO {

    @Serial
    private static final long serialVersionUID = -1130271655784192982L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 是否有效(0-无效，1-有效)
     */
    @Schema(description = "是否有效(0-无效，1-有效)")
    private Boolean activeFlag;

    /**
     * 基础价格
     */
    @Schema(description = "基础价格")
    private BigDecimal baseFee;

    /**
     * 删除原因
     */
    @Schema(description = "删除原因")
    private String deletedNote;

    /**
     * 计费起始阈值（实际数量超过该值时触发计费）
     */
    @Schema(description = "计费起始阈值（实际数量超过该值时触发计费）")
    private BigDecimal feeStartThreshold;

    /**
     * 计费单位类型
     */
    @Schema(description = "计费单位类型")
    private String feeUnitType;

    /**
     * header表id
     */
    @Schema(description = "header表id")
    private Long headerId;

    /**
     * 行序号
     */
    @Schema(description = "行序号")
    private Integer lineNum;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 结束（不包含）根据condition_type得到值来判断
     */
    @Schema(description = "结束（不包含）根据condition_type得到值来判断")
    private Long sectionEnd;

    /**
     * 开始（包含）根据condition_type得到值来判断
     */
    @Schema(description = "开始（包含）根据condition_type得到值来判断")
    private Long sectionStart;

    /**
     * 单价
     */
    @Schema(description = "单价")
    private BigDecimal unitFee;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;


}