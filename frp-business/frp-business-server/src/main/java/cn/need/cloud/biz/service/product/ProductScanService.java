package cn.need.cloud.biz.service.product;

import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.entity.product.ProductScan;
import cn.need.cloud.biz.model.param.product.create.ProductScanCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductScanUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductScanQuery;
import cn.need.cloud.biz.model.query.product.ScanNumAndPartnerQuery;
import cn.need.cloud.biz.model.vo.product.BaseProductVersionInfoVO;
import cn.need.cloud.biz.model.vo.product.ProductScanVO;
import cn.need.cloud.biz.model.vo.product.page.ProductScanPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 产品扫描 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface ProductScanService extends SuperService<ProductScan> {

    /**
     * 根据参数新增产品扫描
     *
     * @param createParam 请求创建参数，包含需要插入的产品扫描的相关信息
     * @return 产品扫描ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(ProductScanCreateParam createParam);

    /**
     * 根据产品ID获取多箱扫描信息
     *
     * @param productId 产品ID，用于指定需要查询多箱扫描信息的产品
     * @return 返回一个包含产品多箱扫描信息的列表，如果未找到相关记录，则返回空列表
     */
    List<ProductScan> getProductMultiboxScans(Long productId);

    /**
     * 根据产品ID删除记录
     *
     * @param productId 需要删除的记录所关联的产品ID
     */
    void deleteByProductId(Long productId);

    /**
     * 根据扫描编号和交易合作伙伴ID获取产品信息
     *
     * @param query 包含扫描编号和交易合作伙伴ID的查询对象
     * @return 返回一个产品扫描对象，其中包含查询到的产品详情
     */
    ProductScanVO productByScanNumAndTransactionPartnerId(ScanNumAndPartnerQuery query);

    /**
     * 根据参数更新产品扫描
     *
     * @param updateParam 请求创建参数，包含需要更新的产品扫描的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(ProductScanUpdateParam updateParam);

    /**
     * 根据查询条件获取产品扫描列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品扫描对象的列表(分页)
     */
    List<ProductScanPageVO> listByQuery(ProductScanQuery query);

    /**
     * 根据查询条件获取产品扫描列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品扫描对象的列表(分页)
     */
    PageData<ProductScanPageVO> pageByQuery(PageSearch<ProductScanQuery> search);

    /**
     * 根据ID获取产品扫描
     *
     * @param id 产品扫描ID
     * @return 返回产品扫描VO对象
     */
    ProductScanVO detailById(Long id);

    /**
     * 初始化插入扫描操作
     *
     * @param entity 产品实体，包含需要进行插入扫描操作的产品相关信息
     */
    void initInsertScanByProduct(Product entity);

    /**
     * 插入或更新多个产品多选框关联信息
     *
     * @param newList 产品多箱新集合
     */
    void insertOrUpdateByMultibox(Set<ProductMultibox> newList, Long productId);

    /**
     * 根据多个ID删除实体
     *
     * @param idList 一个包含多个实体ID的列表，用于指定待删除的实体
     */
    void deleteByMultiboxIds(List<Long> idList);

    /**
     * 根据扫描编号获取产品扫描
     *
     * @param query 包含扫描编号的查询对象
     * @return 返回一个产品扫描对象，其中包含查询到的产品详情
     */
    List<ProductScanVO> productByScanNum(ScanNumAndPartnerQuery query);

    /**
     * 更新产品扫描表中的供应商SKU和UPC相关数据
     * <p>
     * 该方法会同步更新指定产品的扫描表中与供应商SKU和UPC相关的扫描编号。
     * 确保扫描表数据与主产品数据保持一致。
     * </p>
     *
     * @param productId      产品ID
     * @param oldSupplierSku 旧的供应商SKU
     * @param newSupplierSku 新的供应商SKU
     * @param oldUpc         旧的UPC码
     * @param newUpc         新的UPC码
     */
    void updateScanDataForProduct(Long productId, String oldSupplierSku, String newSupplierSku, String oldUpc, String newUpc);

    /**
     * 根据扫描编号获取产品扫描
     *
     * @param query 包含扫描编号的查询对象
     * @return 返回一个产品扫描对象，其中包含查询到的产品详情
     */
    List<BaseProductVersionInfoVO> productVersionByScanNum(ScanNumAndPartnerQuery query);
}