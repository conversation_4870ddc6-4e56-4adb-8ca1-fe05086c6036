package cn.need.cloud.biz.service.otc.workorder.impl;

import cn.need.cloud.biz.client.constant.enums.base.PrepWordorderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderDetailTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.ProcessType;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPrepWorkOrderEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcPrepWorkorderStatusEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductConfigTypeEnum;
import cn.need.cloud.biz.client.constant.workorder.WorkorderLogConstant;
import cn.need.cloud.biz.converter.otc.OtcPrepWorkorderConverter;
import cn.need.cloud.biz.mapper.otc.OtcPrepWorkorderMapper;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.OtcPrepPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.BasePickVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipDetailPickVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipPickContextVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipPutAwayContextVO;
import cn.need.cloud.biz.model.vo.otc.workorder.*;
import cn.need.cloud.biz.model.vo.page.OtcPrepWorkorderPageVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPrepWorkorderAuditLogHelper;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderBinLocationService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.service.product.ProductSpecialService;
import cn.need.cloud.biz.util.AllocationUtil;
import cn.need.cloud.biz.util.DropListUtil;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC预提工单 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcPrepWorkorderServiceImpl extends SuperServiceImpl<OtcPrepWorkorderMapper, OtcPrepWorkorder> implements OtcPrepWorkorderService {

    @Resource
    @Lazy
    private OtcWorkorderService otcWorkorderService;
    @Resource
    private OtcPrepPickingSlipService otcPrepPickingSlipService;
    @Resource
    private OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;
    @Resource
    private OtcPrepWorkorderBinLocationService otcPrepWorkorderBinLocationService;
    @Resource
    private ProductService productService;
    @Resource
    private PickingSlipService pickingSlipService;
    @Resource
    private BinLocationDetailService binLocationDetailService;
    @Resource
    private ProductSpecialService productSpecialService;

    /**
     * 构建Prep工单拣货信息
     *
     * @param pickingSlipPickList           Prep拣货单拣货信息
     * @param prepDetailGroupByProductIdMap Prep工单详情产品分组
     * @return /
     */
    private static List<OtcPrepWorkorderDetailPickVO> buildPickList(List<OtcPrepPickingSlipDetailPickVO> pickingSlipPickList,
                                                                    Map<Long, List<OtcPrepWorkorderDetail>> prepDetailGroupByProductIdMap,
                                                                    Map<Long, OtcPrepWorkorder> prepWorkOrderMap) {
        // 根据工单RefNum排序 分配产品
        prepDetailGroupByProductIdMap.forEach((key, detailList) ->
                detailList.sort(Comparator.comparing(o -> prepWorkOrderMap.get(o.getOtcPrepWorkorderId()).getRefNum())));
        // 分配
        return AllocationUtil.checkAndAllocationPickQty(
                pickingSlipPickList,
                prepDetailGroupByProductIdMap,
                (detail, pick) -> buildPrepWorkorderDetailPick(prepWorkOrderMap, pick, detail)
        );

    }

    /**
     * 构建工单详情拣货信息
     *
     * @param prepWorkOrderMap Prep工单
     * @param pick             拣货单拣货信息
     * @param detail           Prep工单详情
     * @return /
     */
    @NotNull
    private static OtcPrepWorkorderDetailPickVO buildPrepWorkorderDetailPick(Map<Long, OtcPrepWorkorder> prepWorkOrderMap,
                                                                             OtcPrepPickingSlipDetailPickVO pick,
                                                                             OtcPrepWorkorderDetail detail) {
        OtcPrepWorkorderDetailPickVO workorderPick = BeanUtil.copyNew(detail, OtcPrepWorkorderDetailPickVO.class);
        workorderPick.setProductId(pick.getProductId());
        workorderPick.setProductVersionId(pick.getProductVersionId());

        // 设置库位相关信息
        workorderPick.setBinLocationId(pick.getBinLocationId());
        workorderPick.setBinLocationDetailId(pick.getBinLocationDetailId());
        workorderPick.setBinLocationDetailLockedId(pick.getBinLocationDetailLockedId());

        // 设置拣货单信息
        workorderPick.setOtcPrepPickingSlipDetailId(pick.getId());
        workorderPick.setOtcPrepPickingSlipId(pick.getOtcPrepPickingSlipId());

        // 设置工单
        workorderPick.setOtcWorkorderId(prepWorkOrderMap.get(detail.getOtcPrepWorkorderId()).getOtcWorkorderId());
        workorderPick.setOtcWorkorderDetailId(prepWorkOrderMap.get(detail.getOtcPrepWorkorderId()).getOtcWorkorderDetailId());

        // 锁相关信息
        workorderPick.setRefTableId(workorderPick.getId());
        workorderPick.setRefTableRefNum(String.valueOf(workorderPick.getLineNum()));
        workorderPick.setRefTableName(OtcPrepWorkorderDetail.class.getSimpleName());
        workorderPick.setRefTableShowName(OtcPrepWorkorder.class.getSimpleName());
        workorderPick.setRefTableShowRefNum(Optional.ofNullable(prepWorkOrderMap.get(detail.getOtcPrepWorkorderId()))
                .map(OtcPrepWorkorder::getRefNum)
                .orElse(String.valueOf(workorderPick.getOtcPrepWorkorderId()))
        );

        // 库位日志信息
        RefTableBO logInfo = new RefTableBO();
        logInfo.setRefTableId(pick.getRefTableId());
        logInfo.setRefTableRefNum(pick.getRefTableRefNum());
        logInfo.setRefTableName(pick.getRefTableName());
        logInfo.setRefTableShowName(pick.getRefTableShowName());
        logInfo.setRefTableShowRefNum(pick.getRefTableShowRefNum());
        workorderPick.setPickLogInfo(logInfo);

        workorderPick.setPickedBeforeQty(detail.getPickedQty());
        return workorderPick;
    }

    /**
     * 修复库位查询条件为空对象，影响SQL语句拼接
     *
     * @param query 工单列表查询条件
     * @return /
     */
    private static boolean checkAndFixBinLocationQuery(OtcPrepWorkOrderListQuery query) {
        OtcPrepWorkorderQuery otcWorkorderQuery = query.getOtcPrepWorkorderQuery();
        BaseBinLocationQuery baseBinLocationQuery = query.getBinLocationQuery();
        boolean canBinLocationQuery = ObjectUtil.isNotNull(otcWorkorderQuery)
                // 仅New状态才开启
                && Objects.equals(otcWorkorderQuery.getPrepWorkorderStatus(), OtcPrepWorkorderStatusEnum.BEGIN.getStatus())
                // 添加库位查询条件
                && ObjectUtil.isNotNull(baseBinLocationQuery)
                // 至少存在一个条件
                && baseBinLocationQuery.enable();
        if (!canBinLocationQuery) {
            // 未开启库位查询条件 库位查询条件设置null，空对象影响查询SQL
            query.setBinLocationQuery(null);
        }
        return canBinLocationQuery;
    }

    /**
     * 校验FilterBuild查询条件
     *
     * @param search 查询条件
     */
    private static void checkFilterBuildQuery(OtcPrepWorkOrderListQuery search) {
        OtcPrepWorkorderQuery prepWorkOrderQuery = search.getOtcPrepWorkorderQuery();
        boolean noSelectShipDate = ObjectUtil.isNull(prepWorkOrderQuery.getLastShipDateEnd())
                || ObjectUtil.isNull(prepWorkOrderQuery.getLastShipDateStart());
        if (noSelectShipDate) {
            throw new BusinessException("Please select lastShipDate");
        }

        // 仅仅New状态Filter Build Picking Slip
        boolean notNewWorkOrderStatus = !OtcPrepWorkorderStatusEnum.BEGIN.getStatus().equals(prepWorkOrderQuery.getPrepWorkorderStatus());
        if (notNewWorkOrderStatus) {
            throw new BusinessException("Only support Begin Prep Work Order status");
        }

        // 校验processType
        String processType = prepWorkOrderQuery.getProcessType();
        Validate.isTrue(ProcessType.NORMAL.getType().equals(processType),
                "Please select ProcessType such as {} can be built", ProcessType.NORMAL.getType()
        );
    }

    /// ///////////////////////////////////////// 共有方法 ////////////////////////////////////////////

    @Override
    public PageData<OtcPrepWorkorderPageVO> pageByQuery(PageSearch<OtcPrepWorkOrderListQuery> search) {
        Page<OtcPrepWorkorder> page = Conditions.page(search, entityClass);
        OtcPrepWorkOrderListQuery condition = search.getCondition();
        // 存在库位条件的处理
        if (!this.fixQueryByBinLocation(condition)) {
            return new PageData<>(Collections.emptyList(), page);
        }
        List<OtcPrepWorkorderPageVO> dataList = mapper.listByQuery(condition.getOtcPrepWorkorderQuery(), condition.getBinLocationQuery(), page);
        // 填充关联VO
        this.fillRelationVO(dataList);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtcPrepWorkorderVO detailById(Long id) {
        OtcPrepWorkorder entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in OtcPrepWorkorder");
        }
        return buildOtcPrepWorkorderVO(entity);
    }

    @Override
    public OtcPrepWorkorderVO detailByRefNum(String refNum) {
        OtcPrepWorkorder entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in OtcPrepWorkorder");
        }
        return buildOtcPrepWorkorderVO(entity);
    }

    @Override
    public OtcWorkorderFilterBuildPickingSlipCountVO filterBuildPickingSlipCount(OtcPrepWorkOrderListQuery search) {
        checkFilterBuildQuery(search);
        // 修复仓库条件空对象SQL问题
        fixQueryByBinLocation(search);

        // 构建返回结果
        OtcWorkorderFilterBuildPickingSlipCountVO countVO = new OtcWorkorderFilterBuildPickingSlipCountVO();
        if (!this.fixQueryByBinLocation(search)) {
            countVO.setFilterSum(0L);
            return countVO;
        }
        // 统计
        Long filterSum = mapper.filterBuildPickingSlipCount(search.getOtcPrepWorkorderQuery(), search.getBinLocationQuery());
        countVO.setFilterSum(filterSum);
        return countVO;
    }

    @Override
    public List<OtcPrepWorkorder> listByWorkOrderIdList(List<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(OtcPrepWorkorder::getOtcWorkorderId, workOrderIdList).list();
    }

    @Override
    public OtcPrepWorkorder multiBoxPrepByWorkOrderDetailId(Long workOrderDetailId) {
        return lambdaQuery().
                eq(OtcPrepWorkorder::getOtcWorkorderDetailId, workOrderDetailId)
                .in(OtcPrepWorkorder::getPrepWorkorderType, Arrays.asList(
                        PrepWordorderTypeEnum.PREP_MULTI_BOX.getType(),
                        PrepWordorderTypeEnum.PREP_CONVERT_MULTI_BOX.getType())
                )
                .last("limit 1")
                .one();
    }

    @Override
    public List<OtcPrepWorkorder> filterBuildByQuery(OtcPrepPickingSlipFilterBuildQuery query) {
        OtcPrepWorkOrderListQuery workOrderListQuery = query.getFilter();
        checkFilterBuildQuery(workOrderListQuery);

        // 限制最大工单数
        Integer maxWorkOrderCount = query.getStrategy().getMaxPrepWorkOrderCount();
        PageSearch<OtcPrepWorkOrderListQuery> searchPage = new PageSearch<>(1, maxWorkOrderCount, workOrderListQuery);
        // 修复库位查询条件
        checkAndFixBinLocationQuery(workOrderListQuery);
        // 这里查询没有校验库存
        List<OtcPrepWorkorderPageVO> dataList = mapper.listByQuery(workOrderListQuery.getOtcPrepWorkorderQuery(),
                workOrderListQuery.getBinLocationQuery(), Conditions.page(searchPage, entityClass));
        return BeanUtil.copyNew(dataList, OtcPrepWorkorder.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pick(OtcPrepPickingSlipPickContextVO context) {
        OtcPrepPickingSlip prepPickingSlip = context.getPrepPickingSlip();
        // 工单
        Map<Long, OtcPrepWorkorder> prepWorkOrderMap = this.findCanPickListByPickingSlip(prepPickingSlip);
        List<Long> prepWorkOrderIdList = prepWorkOrderMap.keySet().stream().toList();

        // 工单详情分组
        List<OtcPrepWorkorderDetail> prepWorkOrderDetailList = otcPrepWorkorderDetailService.groupByOtcPrepWorkOrderIdList(prepWorkOrderIdList)
                .values().stream()
                .flatMap(Collection::stream)
                .toList();

        // 产品工单详情分组
        Map<Long, List<OtcPrepWorkorderDetail>> prepDetailGroupByProductIdMap = prepWorkOrderDetailList
                .stream()
                .collect(Collectors.groupingBy(OtcPrepWorkorderDetail::getProductId));

        // 构建Prep工单详情拣货信息
        List<OtcPrepWorkorderDetailPickVO> prepWorkOrderDetailPickUpdateList
                = buildPickList(context.getPickAfterPrepDetailList(), prepDetailGroupByProductIdMap, prepWorkOrderMap);

        // 更新库位锁并扣减库位库存
        pickingSlipService.otcMoveBinLocationInventoryToReadyToGo(prepWorkOrderDetailPickUpdateList);

        this.updatePickedQtyAndPicked(prepWorkOrderMap, prepWorkOrderDetailList, prepWorkOrderDetailPickUpdateList);

        return true;
    }

    @Override
    public List<DropProVO> distinctValuePro(OtcPrepWorkorderQuery query) {
        return DropListUtil.dropProList(
                query.getColumnNameList(),
                OtcPrepWorkorder.class,
                columnInfos -> mapper.dropProList(columnInfos, null, query)
        );
    }

    @Override
    public List<Long> workorderIdListByPrepPickingSlipId(Long prepPickingSlipId) {

        return this.lambdaQuery()
                .select(OtcPrepWorkorder::getOtcWorkorderId)
                .eq(OtcPrepWorkorder::getOtcPrepPickingSlipId, prepPickingSlipId)
                .list()
                .stream()
                .map(OtcPrepWorkorder::getOtcWorkorderId)
                .toList();
    }

    @Override
    public List<OtcPrepWorkorder> listByPrepPickingSlipId(Long prepPickingSlipId) {
        return lambdaQuery()
                .eq(OtcPrepWorkorder::getOtcPrepPickingSlipId, prepPickingSlipId)
                .list();
    }

    @Override
    public List<OtcPrepWorkorder> listByPrepPickingSlipIds(List<Long> prepPickingSlipIdList) {
        if (ObjectUtil.isEmpty(prepPickingSlipIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(OtcPrepWorkorder::getOtcPrepPickingSlipId, prepPickingSlipIdList)
                .list();
    }

    @Override
    public List<OtcPrepWorkorder> listByWorkorderDetailIds(Collection<Long> longs) {
        if (ObjectUtil.isNotEmpty(longs)) {
            return lambdaQuery().in(OtcPrepWorkorder::getOtcWorkorderDetailId, longs).list();
        }
        return List.of();
    }

    @Override
    public List<OtcPrepWorkorder> putAwayListByPrepPickingSlipId(Long otcPrepPickingSlipId) {
        return lambdaQuery()
                .eq(OtcPrepWorkorder::getOtcPrepPickingSlipId, otcPrepPickingSlipId)
                .orderByAsc(OtcPrepWorkorder::getRefNum)
                .list();
    }

    /// ///////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAndSetWorkOrderProcessed(OtcPrepPickingSlipPutAwayContextVO context) {
        List<OtcPrepWorkorder> prepWorkOrderList = context.getPutAwayPrepWorkOrderList();
        if (ObjectUtil.isEmpty(prepWorkOrderList)) {
            return;
        }

        // 已经处理完成Prep工单
        List<OtcPrepWorkorder> processedList = prepWorkOrderList.stream()
                .filter(obj -> Objects.equals(obj.getPrepWorkorderStatus(), OtcPrepWorkorderStatusEnum.PROCESSED.getStatus()))
                .toList();

        List<Long> workOrderIdList = StreamUtils.distinctMap(processedList, OtcPrepWorkorder::getOtcWorkorderId);

        int prepWorkOrderCount = this.updateBatch(prepWorkOrderList);
        Validate.isTrue(prepWorkOrderCount == prepWorkOrderList.size(), "Failed to update prep work order");

        // 设置工单状态为已处理
        Map<Long, List<OtcPrepWorkorder>> prepWorkorderGroupByWkMap = this.groupByWorkorderIdList(workOrderIdList);

        List<Long> processedWorkorderIdList = prepWorkorderGroupByWkMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue()
                        .stream()
                        .allMatch(prep -> prep.getPrepWorkorderStatus().equals(OtbPrepWorkOrderEnum.PROCESSED.getStatus())))
                .map(Map.Entry::getKey)
                .toList();

        List<OtcWorkorder> workorderList = otcWorkorderService.listByIds(processedWorkorderIdList);
        workorderList.forEach(obj -> obj.setWorkorderPrepStatus(WorkOrderPrepStatusEnum.PROCESSED.getStatus()));
        int workOrderUpdateCount = otcWorkorderService.updateBatch(workorderList);
        Validate.isTrue(workOrderUpdateCount == workorderList.size(), "Failed to update work order");

        // workorder_prep_status Processed
        context.setWorkorderList(workorderList);
    }

    private Map<Long, List<OtcPrepWorkorder>> groupByWorkorderIdList(List<Long> workOrderIdList) {
        if (ObjectUtil.isEmpty(workOrderIdList)) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .in(OtcPrepWorkorder::getOtcWorkorderId, workOrderIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OtcPrepWorkorder::getOtcWorkorderId));
    }

    @Override
    public Map<Long, List<OtcPrepWorkorder>> groupByPrepPickingSlipIdList(List<Long> prepPickingSlipIdList) {
        if (ObjectUtil.isEmpty(prepPickingSlipIdList)) {
            return Collections.emptyMap();
        }
        // 按拣货单分组
        return lambdaQuery()
                .in(OtcPrepWorkorder::getOtcPrepPickingSlipId, prepPickingSlipIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OtcPrepWorkorder::getOtcPrepPickingSlipId));
    }

    /**
     * 初始化OTC预提工单对象
     * 此方法用于设置OTC预提工单对象的必要参数，确保其处于有效状态
     *
     * @param entity OTC预提工单对象，不应为空
     * @return 返回初始化后的OTC预提工单
     * @throws BusinessException 如果传入的OTC预提工单为空，则抛出此异常
     */
    private OtcPrepWorkorder initOtcPrepWorkorder(OtcPrepWorkorder entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("OtcPrepWorkorder cannot be empty");
        }

        // 生成RefNum
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PREP_WORK_ORDER.getCode()));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建OTC预提工单VO对象
     *
     * @param entity OTC预提工单对象
     * @return 返回包含详细信息的OTC预提工单VO对象
     */
    private OtcPrepWorkorderVO buildOtcPrepWorkorderVO(OtcPrepWorkorder entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的OTC预提工单VO对象
        OtcPrepWorkorderVO vo = Converters.get(OtcPrepWorkorderConverter.class).toVO(entity);
        // 填充拣货单
        vo.setOtcPrepPickingSlip(otcPrepPickingSlipService.refNumById(vo.getOtcPrepPickingSlipId()));
        // 填充工单
        OtcWorkorderNoDetailVO workOrder = otcWorkorderService.noDetailById(vo.getOtcWorkorderId());
        if (ObjectUtil.isNotNull(workOrder)) {
            vo.setOtcWorkOrder(BeanUtil.copyNew(workOrder, OtcPrepWorkOrderOtcWorkOrderVO.class));
        }
        // 填充Prep工单Details
        List<OtcPrepWorkorderDetail> details = otcPrepWorkorderDetailService.listByPrepWorkOrderId(vo.getId());
        vo.setDetailList(BeanUtil.copyNew(details, OtcPrepWorkorderDetailVO.class));
        // 填充产品
        vo.setBaseProductVO(productService.baseProductById(vo.getProductId()));
        return vo;
    }

    /**
     * 填充关联VO
     *
     * @param dataList dataList
     */
    private void fillRelationVO(List<OtcPrepWorkorderPageVO> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        // 工单
        List<Long> workOrderIds = StreamUtils.distinctMap(dataList, OtcPrepWorkorderPageVO::getOtcWorkorderId);
        Map<Long, OtcWorkorderNoDetailVO> workOrderMap = otcWorkorderService.noDetailByIds(workOrderIds);
        dataList.stream()
                .filter(obj -> workOrderMap.containsKey(obj.getOtcWorkorderId()))
                .forEach(obj -> obj.setOtcWorkOrder(BeanUtil.copyNew(workOrderMap.get(obj.getOtcWorkorderId()), OtcPrepWorkOrderOtcWorkOrderVO.class)));

        // 拣货单
        List<Long> pickingSlipIds = StreamUtils.distinctMap(dataList, OtcPrepWorkorderPageVO::getOtcPrepPickingSlipId);
        Map<Long, RefNumVO> pickingSlipMap = otcPrepPickingSlipService.refNumMapByIds(pickingSlipIds);
        dataList.forEach(obj -> obj.setOtcPrepPickingSlip(pickingSlipMap.get(obj.getOtcPrepPickingSlipId())));
    }

    /**
     * 根据库位查询条件，修正查询条件
     *
     * @param query 列表查询条件
     */
    private boolean fixQueryByBinLocation(OtcPrepWorkOrderListQuery query) {
        boolean canBinLocationQuery = checkAndFixBinLocationQuery(query);
        // 未开启直接返回
        if (!canBinLocationQuery) {
            return true;
        }
        // 开启工单库存校验
        List<OtcPrepWorkorderPageVO> dataList = mapper.listByQuery(query.getOtcPrepWorkorderQuery(), query.getBinLocationQuery());
        // 校验库存
        List<Long> hasStockWorkOrderIdList = this.listCheckWorkOrderInStock(dataList, query.getBinLocationQuery());
        if (ObjectUtil.isEmpty(hasStockWorkOrderIdList)) {
            return false;
        }
        // 清空查询条件 使用工单id查询
        query.setBinLocationQuery(null);
        query.setOtcPrepWorkorderQuery(null);
        OtcPrepWorkorderQuery fixWorkOrderQuery = new OtcPrepWorkorderQuery();
        fixWorkOrderQuery.setIdList(hasStockWorkOrderIdList);
        query.setOtcPrepWorkorderQuery(fixWorkOrderQuery);
        return true;
    }

    /**
     * 校验工单库存
     *
     * @param dataList 工单集合
     * @return 有库存的工单
     */
    private List<Long> listCheckWorkOrderInStock(List<OtcPrepWorkorderPageVO> dataList, BaseBinLocationQuery binLocationQuery) {
        // 校验库存
        List<Long> workOrderIdList = StreamUtils.distinctMap(dataList, OtcPrepWorkorderPageVO::getId);
        // 详情按工单分组
        Map<Long, List<OtcPrepWorkorderDetail>> detailGroupByWorkOrderMap = otcPrepWorkorderDetailService.groupByOtcPrepWorkOrderIdList(workOrderIdList)
                .values()
                .stream()
                .flatMap(Collection::stream)
                .filter(obj -> Objects.equals(obj.getPrepWorkorderDetailType(), PrepWorkOrderDetailTypeEnum.NONE.getStatus()))
                .collect(Collectors.groupingBy(OtcPrepWorkorderDetail::getOtcPrepWorkorderId));

        // 产品id集合
        List<Long> productIdList = detailGroupByWorkOrderMap.values().stream()
                .flatMap(Collection::stream)
                .map(OtcPrepWorkorderDetail::getProductId)
                .distinct()
                .toList();

        // 产品可用库存映射
        Map<Long, Integer> productAvailableStockMap = binLocationDetailService.realAvailableInStockGroupByProductId(binLocationQuery, productIdList);
        return StreamUtils.filterHasProductStock(StreamUtils.sortByVOList(detailGroupByWorkOrderMap, dataList), productAvailableStockMap);
    }

    /**
     * 构建预处理工单基础信息
     */
    @Override
    public OtcPrepWorkorder createBasePrepWorkorder(
            final OtcWorkorder workOrder,
            final OtcWorkorderDetail detail,
            final PrepWorkOrderTypeEnum prepWorkOrderType,
            final InventoryReserve inventoryReserve
    ) {
        OtcPrepWorkorder prepWorkorder = new OtcPrepWorkorder();
        prepWorkorder.setOtcWorkorderId(workOrder.getId());
        prepWorkorder.setOtcWorkorderDetailId(detail.getId());
        prepWorkorder.setQty(detail.getQty());
        prepWorkorder.setPrepWorkorderStatus(OtcPrepWorkorderStatusEnum.BEGIN.getStatus());
        prepWorkorder.setPrepWorkorderType(prepWorkOrderType.getStatus());
        prepWorkorder.setRefNum(getOtcPrepWorkOrderRefNum());
        prepWorkorder.setProductId(detail.getProductId());
        prepWorkorder.setPutawayQty(0);
        prepWorkorder.setInventoryReserveId(inventoryReserve.getId());
        prepWorkorder.setOrderType(workOrder.getOrderType());
        prepWorkorder.setPickToStation(workOrder.getPickToStation());
        prepWorkorder.setHasCusShipRequire(workOrder.getRequestSnapshotHasCusShipRequire());
        prepWorkorder.setTransactionPartnerId(workOrder.getRequestSnapshotTransactionPartnerId());
        prepWorkorder.setOnSitePackFlag(prepWorkorder.getOnSitePackFlag());

        Integer productVersionInt = null;
        switch (prepWorkOrderType) {
            case PREPPACK -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.ASSEMBLY);
            }
            case PREPMULTIBOX -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.MULTIBOX);
            }
            case PREPCONVERT -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.GROUP);
            }
            case PREPCONVERTPACK -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.ASSEMBLY);
            }
            case PREPCONVERTMULTIBOX -> {
                productVersionInt = productSpecialService.getProductVersionInt(prepWorkorder.getProductId(), ProductConfigTypeEnum.MULTIBOX);
            }
            default -> throw new IllegalStateException("Unexpected value: " + prepWorkOrderType);
        }

        prepWorkorder.setPrepWorkorderVersionInt(productVersionInt);
        prepWorkorder.setId(IdWorker.getId());
        return prepWorkorder;
    }

    private String getOtcPrepWorkOrderRefNum() {
        return FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PREP_WORK_ORDER);
    }

    /**
     * 获取可拣货工单
     *
     * @param prepPickingSlip prepPickingSlip
     * @return /
     */
    @NotNull
    private Map<Long, OtcPrepWorkorder> findCanPickListByPickingSlip(OtcPrepPickingSlip prepPickingSlip) {
        return this.lambdaQuery().eq(OtcPrepWorkorder::getOtcPrepPickingSlipId, prepPickingSlip.getId())
                .eq(OtcPrepWorkorder::getPrepWorkorderStatus, OtcPrepWorkorderStatusEnum.IN_PICKING.getStatus())
                .list()
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
    }

    /**
     * 更新拣货数量
     *
     * @param prepWorkOrderMap                  Prep工单
     * @param prepWorkOrderDetailList           Prep工单详情
     * @param prepWorkOrderDetailPickUpdateList Prep工单详情拣货信息
     */
    private void updatePickedQtyAndPicked(Map<Long, OtcPrepWorkorder> prepWorkOrderMap,
                                          List<OtcPrepWorkorderDetail> prepWorkOrderDetailList,
                                          List<OtcPrepWorkorderDetailPickVO> prepWorkOrderDetailPickUpdateList) {
        // 增加otc_workorder_bin_location
        List<OtcPrepWorkorderBinLocation> prepWorkOrderBinLocationList = prepWorkOrderDetailPickUpdateList.stream()
                .map(obj -> {
                    OtcPrepWorkorderBinLocation wkBinLocation = BeanUtil.copyNew(obj, OtcPrepWorkorderBinLocation.class);
                    wkBinLocation.setId(IdWorker.getId());
                    wkBinLocation.setOtcPrepWorkorderDetailId(obj.getId());
                    obj.setOtcPrepWorkorderBinLocationId(wkBinLocation.getId());
                    // 分配至库位的数量
                    wkBinLocation.setQty(obj.getChangePickQty());
                    // readyToGo lock_id
                    wkBinLocation.setBinLocationDetailLockedId(obj.getReadyToGoLocked().getId());
                    return wkBinLocation;
                })
                .toList();
        otcPrepWorkorderBinLocationService.insertBatch(prepWorkOrderBinLocationList);

        // 需要更新的工单详情
        Map<Long, Integer> detailPickChangeMap = prepWorkOrderDetailPickUpdateList.stream()
                .collect(Collectors.groupingBy(OtcPrepWorkorderDetailPickVO::getId,
                        Collectors.mapping(BasePickVO::getChangePickQty, Collectors.summingInt(Integer::intValue)))
                );
        List<OtcPrepWorkorderDetail> pickDetailUpdateList = prepWorkOrderDetailList.stream()
                .filter(obj -> detailPickChangeMap.containsKey(obj.getId()))
                .toList();

        // 更新拣货数量
        List<OtcPrepWorkorderDetail> detailUpdateList = pickingSlipService.updateDetailPickQty(prepWorkOrderDetailList, pickDetailUpdateList);
        // 更新工单详情
        int wkDetailUpdateCount = otcPrepWorkorderDetailService.updateBatch(detailUpdateList);
        Validate.isTrue(wkDetailUpdateCount == detailUpdateList.size(), "Failed to update work order detail");

        // 更新工单状态
        List<OtcPrepWorkorder> updateWorkorderList = prepWorkOrderDetailList.stream()
                .collect(Collectors.groupingBy(OtcPrepWorkorderDetail::getOtcPrepWorkorderId))
                .entrySet()
                .stream()
                .map(entry -> {
                    // 全部拣货完
                    List<OtcPrepWorkorderDetail> detailList = entry.getValue();
                    boolean allPicked = detailList.stream()
                            .allMatch(obj -> Objects.equals(obj.getPickedQty(), obj.getQty()))
                            || ObjectUtil.isEmpty(detailList);
                    OtcPrepWorkorder workorder = prepWorkOrderMap.get(entry.getKey());
                    // 更新状态
                    workorder.setPrepWorkorderStatus(allPicked
                            ? OtcPrepWorkorderStatusEnum.PICKED.getStatus()
                            : workorder.getPrepWorkorderStatus()
                    );
                    return workorder;
                })
                .filter(obj -> Objects.equals(obj.getPrepWorkorderStatus(), OtcPrepWorkorderStatusEnum.PICKED.getStatus()))
                .toList();

        Validate.isTrue(super.updateBatch(updateWorkorderList) == updateWorkorderList.size(),
                "Failed to update PrepWorkorder FinishPicking status"
        );

        // Prep工单: InPicking -> Picked 日志
        for (OtcPrepWorkorder prepWorkorder : updateWorkorderList) {
            OtcPrepWorkorderAuditLogHelper.recordLog(prepWorkorder, WorkorderLogConstant.PICKED_DESCRIPTION, null);
        }
    }

}
