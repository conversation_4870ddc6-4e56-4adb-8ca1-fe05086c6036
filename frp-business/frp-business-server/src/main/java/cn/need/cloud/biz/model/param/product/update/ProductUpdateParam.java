package cn.need.cloud.biz.model.param.product.update;

import cn.need.cloud.biz.jackson.UpperCase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 产品 update对象
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Schema(description = "产品 update对象")
public class ProductUpdateParam implements Serializable {


    @Serial
    private static final long serialVersionUID = 5054091976050866685L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU")
    @UpperCase
    private String supplierSku;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    @UpperCase
    private String upc;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;


}