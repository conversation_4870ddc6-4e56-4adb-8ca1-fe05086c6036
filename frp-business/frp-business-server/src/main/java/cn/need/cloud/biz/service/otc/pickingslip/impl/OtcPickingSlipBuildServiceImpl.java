package cn.need.cloud.biz.service.otc.pickingslip.impl;

import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryLockedStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcBuildPickingSlipStrategyEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcBuildShipPackageEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.otc.OtcWorkorderStatusEnum;
import cn.need.cloud.biz.client.constant.workorder.WorkorderLogConstant;
import cn.need.cloud.biz.model.bo.base.BaseModelInventoryReserveBO;
import cn.need.cloud.biz.model.bo.otc.OtcPickingSlipSplitZoneBO;
import cn.need.cloud.biz.model.bo.otc.OtcWorkorderBinLocationBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipBuildStrategyQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkOrderListQuery;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcFilterBuildContextVO;
import cn.need.cloud.biz.service.base.PickingSlipService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcPickingSlipAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcRequestAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcWorkOrderAuditLogHelper;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipBuildService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipDetailService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.model.IdModel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * OTC拣货单构建服务实现类
 * </p>
 * <p>
 * 该类负责处理与OTC（Outbound To Customer）拣货单构建相关的所有业务逻辑，包括：
 * 1. 根据工单筛选构建拣货单
 * 2. 处理不同类型订单（SOSP、SOMP、多箱、贴标直发）的拣货单构建
 * 3. 执行库位锁定和库存预留
 * 4. 实现拣货单拆分与合并逻辑
 * 5. 处理构建后的状态更新和日志记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@AllArgsConstructor
public class OtcPickingSlipBuildServiceImpl implements OtcPickingSlipBuildService {

    private final OtcPickingSlipDetailService otcPickingSlipDetailService;
    private final OtcPrepWorkorderService otcPrepWorkorderService;
    private final OtcWorkorderService otcWorkorderService;
    private final OtcRequestService otcRequestService;
    private final OtcWorkorderDetailService otcWorkorderDetailService;
    private final BinLocationDetailLockedService binLocationDetailLockedService;
    private final OtcPackageService otcPackageService;
    private final BinLocationService binLocationService;
    private final ProductService productService;
    private final InventoryLockedService inventoryLockedService;
    private final PickingSlipService pickingSlipService;
    private final OtcPickingSlipService otcPickingSlipService;

    /**
     * 聚合拣货单明细
     * <p>
     * 将同一拣货单中相同产品和库位的明细聚合，生成新的明细列表，并按库位名称排序。
     * </p>
     *
     * @param context 拣货单构建上下文，包含原始明细和库位信息
     * @return 聚合后的拣货单明细列表
     * <p>
     * TODO: 聚合逻辑复杂，考虑拆分为多个子方法提高可读性
     * TODO: 使用了硬编码的ID生成方式，建议统一ID生成策略
     */
    @NotNull
    private static List<OtcPickingSlipDetail> aggregateDetailList(OtcFilterBuildContextVO context) {
        List<OtcPickingSlipDetail> pickingSlipDetailList = context.getPickingSlipDetailList();
        Map<Long, BinLocationVO> binLocationMap = context.getBinLocationMap();

        // 按照拣货单分组
        // 使用for循环替代stream方式聚合拣货单明细
        // 按拣货单ID分组
        Map<Long, List<OtcPickingSlipDetail>> groupedByPickingSlip = new HashMap<>();
        for (OtcPickingSlipDetail detail : pickingSlipDetailList) {
            groupedByPickingSlip.computeIfAbsent(detail.getOtcPickingSlipId(), k -> new ArrayList<>()).add(detail);
        }

        List<OtcPickingSlipDetail> result = new ArrayList<>();

        // 对每个拣货单分组进行处理
        for (List<OtcPickingSlipDetail> detailsByPs : groupedByPickingSlip.values()) {
            // 按拣货单、库位详情、产品ID进一步分组
            Map<String, List<OtcPickingSlipDetail>> subGrouped = new HashMap<>();
            for (OtcPickingSlipDetail obj : detailsByPs) {
                String key = String.format("%s:%s:%s", obj.getOtcPickingSlipId(), obj.getBinLocationDetailId(), obj.getProductId());
                subGrouped.computeIfAbsent(key, k -> new ArrayList<>()).add(obj);
            }

            // 按仓库名称排序
            List<List<OtcPickingSlipDetail>> sortedSubGroups = new ArrayList<>(subGrouped.values());
            sortedSubGroups.sort(Comparator.comparing(group -> binLocationMap.get(group.get(0).getBinLocationId()).getLocationName()));

            // 聚合每个子组的数据
            for (int idx = 0; idx < sortedSubGroups.size(); idx++) {
                List<OtcPickingSlipDetail> details = sortedSubGroups.get(idx);
                OtcPickingSlipDetail first = details.get(0);

                OtcPickingSlipDetail merge = new OtcPickingSlipDetail();
                // 复制非聚合属性
                BeanUtil.copy(first, merge);
                merge.setId(IdWorker.getId());
                merge.setLineNum(idx + 1);

                // 计算总数量和已拣货数量
                int totalQty = 0;
                int totalPickedQty = 0;
                for (OtcPickingSlipDetail detail : details) {
                    totalQty += detail.getQty();
                    totalPickedQty += detail.getPickedQty();
                }
                merge.setQty(totalQty);
                merge.setPickedQty(totalPickedQty);

                result.add(merge);
            }
        }

        return result;
    }

    /**
     * 执行拆分逻辑
     * <p>
     * 根据区域和工单维度进行拣货单拆分，确保每个拣货单的SKU数量和库位数量不超过限制。
     * 首先按区域维度拆分，然后按工单维度进一步拆分。
     * </p>
     *
     * @param zoneList       区域维度集合，包含拣货单和工单信息
     * @param context        拣货单构建上下文
     * @param max            最大SKU数量限制
     * @param maxBinLocation 最大库位数量限制
     */
    private static void doSplit(List<OtcPickingSlipSplitZoneBO> zoneList, OtcFilterBuildContextVO context, int max, int maxBinLocation) {
        // 区域拆分
        AllocationUtil.split(zoneList, allocation -> canSplit(allocation, max, maxBinLocation), allocation -> createNew(context, allocation));
        // 分组在工单拆分
        Map<Long, List<OtcPickingSlipSplitZoneBO>> groupedByPickingSlip = new HashMap<>();

        for (OtcPickingSlipSplitZoneBO zone : zoneList) {
            groupedByPickingSlip.computeIfAbsent(zone.getPickingSlipId(), k -> new ArrayList<>()).add(zone);
        }

        for (List<OtcPickingSlipSplitZoneBO> zonesByPs : groupedByPickingSlip.values()) {
            List<OtcWorkorderBinLocationBO> workorderList = new ArrayList<>();
            for (OtcPickingSlipSplitZoneBO zone : zonesByPs) {
                workorderList.addAll(zone.getWorkorderList());
            }


            // 对 workorderList 中的 psDetailList 按 locationName 升序排列
            for (var workorder : zonesByPs) {
                workorder.getPsDetailList().sort(Comparator.comparing(detail -> {
                    BinLocationVO binLocation = context.getBinLocationMap().get(detail.getBinLocationId());
                    return binLocation != null ? binLocation.getLocationName() : "";
                }));
            }

            AllocationUtil.split(workorderList, allocation -> workorderLatitudeCanSplit(allocation, max, maxBinLocation), allocation -> workorderLatitudeCreate(context, allocation));
        }
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 创建新拣货单
     * <p>
     * 基于区域维度拆分结果创建新的拣货单，并更新相关联的工单和明细信息。
     * </p>
     *
     * @param context    拣货单构建上下文
     * @param allocation 分配好的拣货单区域列表
     */
    private static void createNew(OtcFilterBuildContextVO context, List<OtcPickingSlipSplitZoneBO> allocation) {
        if (ObjectUtil.isEmpty(allocation)) {
            return;
        }

        Map<Long, OtcPickingSlip> pickingSlipMap = StreamUtils.toMap(context.getPickingSlipList(), IdModel::getId);
        OtcPickingSlipSplitZoneBO detail = allocation.get(0);

        // 创建新的
        OtcPickingSlip currentPs = pickingSlipMap.get(detail.getPickingSlipId());
        OtcPickingSlip newPickingSlip = new OtcPickingSlip();
        // 拆分新的拣货单
        BeanUtil.copy(currentPs, newPickingSlip);
        newPickingSlip.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PICKING_SLIP.getCode()));
        // 重新设置id
        newPickingSlip.setId(IdWorker.getId());

        Long newPickingSlipId = newPickingSlip.getId();
        // 区域拣货单id
        allocation.forEach(obj -> obj.setPickingSlipId(newPickingSlipId));

        // 工单重新设置拣货单信息
        for (OtcPickingSlipSplitZoneBO zoneBO : allocation) {
            List<OtcWorkorderBinLocationBO> workorderList = zoneBO.getWorkorderList();
            for (OtcWorkorderBinLocationBO workorderBinLocationBO : workorderList) {
                workorderBinLocationBO.getWorkorder().setOtcPickingSlipId(newPickingSlipId);
            }
        }

        // 拣货单详情重新设置拣货单id
        for (OtcPickingSlipSplitZoneBO zoneBO : allocation) {
            List<OtcWorkorderBinLocationBO> workorderList = zoneBO.getWorkorderList();
            for (OtcWorkorderBinLocationBO workorderBinLocationBO : workorderList) {
                List<OtcPickingSlipDetail> psDetailList = workorderBinLocationBO.getPsDetailList();
                for (OtcPickingSlipDetail pickingSlipDetail : psDetailList) {
                    pickingSlipDetail.setOtcPickingSlipId(newPickingSlipId);
                }
            }
        }

        // 添加新的拣货单
        context.getPickingSlipList().add(newPickingSlip);
    }

    /**
     * 判断是否需要按区域维度拆分拣货单
     * <p>
     * 根据SKU数量和库位数量判断拣货单是否需要拆分。
     * 当SKU数量超过最大限制或库位数量超过最大限制时，需要进行拆分。
     * </p>
     *
     * @param dataList       需要判断拆分的区域数据列表
     * @param max            最大SKU数量限制
     * @param maxBinLocation 最大库位数量限制
     * @return 是否需要拆分
     * <p>
     * TODO: 方法名不够直观，建议改为needsSplitByZone或类似名称
     * TODO: 方法返回值注释不准确，应明确说明返回true表示需要拆分
     */
    private static boolean canSplit(List<OtcPickingSlipSplitZoneBO> dataList, int max, int maxBinLocation) {
        if (ObjectUtil.isEmpty(dataList)) {
            return false;
        }
        // 1个就不没办法拆分了
        if (dataList.size() == 1) {
            return false;
        }
        // 统计需要分配的sku数量和库位数量
        int splitSkuCount = dataList.stream()
                .map(OtcPickingSlipSplitZoneBO::getPsDetailList)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(OtcPickingSlipDetail::getProductId))
                .size();

        int splitBinLocationCount = dataList.stream()
                .map(OtcPickingSlipSplitZoneBO::getPsDetailList)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(OtcPickingSlipDetail::getBinLocationId))
                .size();

        // 需要分配的sku数量大于MaxSosp，或者需要分配的库位数量大于MaxSospBinLocation
        return splitSkuCount > max || splitBinLocationCount > maxBinLocation;
    }

    /**
     * 判断是否需要按工单维度拆分拣货单
     * <p>
     * 根据SKU数量和库位数量判断工单组是否需要拆分。
     * 当SKU数量超过最大限制或库位数量超过最大限制时，需要进行拆分。
     * </p>
     *
     * @param dataList       需要判断拆分的工单数据列表
     * @param max            最大SKU数量限制
     * @param maxBinLocation 最大库位数量限制
     * @return 是否需要拆分
     * <p>
     * TODO: 方法名中的"Latitude"拼写有误，应为"Dimension"或类似词汇
     * TODO: 检查逻辑与canSplit方法相似，考虑合并为一个通用方法
     */
    private static boolean workorderLatitudeCanSplit(List<OtcWorkorderBinLocationBO> dataList, int max, int maxBinLocation) {
        if (ObjectUtil.isEmpty(dataList)) {
            return false;
        }
        // 1个就不没办法拆分了
        if (dataList.size() == 1) {
            return false;
        }
        // 统计需要分配的sku数量和库位数量
        int splitSkuCount = dataList.stream().map(OtcWorkorderBinLocationBO::getPsDetailList).flatMap(Collection::stream).collect(Collectors.groupingBy(OtcPickingSlipDetail::getProductId)).size();
        int splitBinLocationCount = dataList.stream().map(OtcWorkorderBinLocationBO::getPsDetailList).flatMap(Collection::stream).collect(Collectors.groupingBy(OtcPickingSlipDetail::getBinLocationId)).size();
        // 需要分配的sku数量大于MaxSosp，或者需要分配的库位数量大于MaxSospBinLocation
        return splitSkuCount > max || splitBinLocationCount > maxBinLocation;
    }

    /**
     * 基于工单维度创建新拣货单
     * <p>
     * 当按工单维度拆分时，为拆分出的工单组创建新的拣货单，并更新相关联的明细信息。
     * </p>
     *
     * @param context    拣货单构建上下文
     * @param allocation 分配好的工单和明细数据
     */
    private static void workorderLatitudeCreate(OtcFilterBuildContextVO context, List<OtcWorkorderBinLocationBO> allocation) {
        if (ObjectUtil.isEmpty(allocation)) {
            return;
        }

        Map<Long, OtcPickingSlip> pickingSlipMap = StreamUtils.toMap(context.getPickingSlipList(), IdModel::getId);
        OtcWorkorderBinLocationBO detail = allocation.get(0);

        // 创建新的
        OtcPickingSlip currentPs = pickingSlipMap.get(detail.getWorkorder().getOtcPickingSlipId());
        OtcPickingSlip newPickingSlip = new OtcPickingSlip();
        // 拆分新的拣货单
        BeanUtil.copy(currentPs, newPickingSlip);
        newPickingSlip.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PICKING_SLIP.getCode()));
        // 重新设置id
        newPickingSlip.setId(IdWorker.getId());

        // 工单重新设置拣货单信息
        allocation.forEach(obj -> obj.getWorkorder().setOtcPickingSlipId(newPickingSlip.getId()));
        // 拣货单详情重新设置拣货单id
        allocation.stream().map(OtcWorkorderBinLocationBO::getPsDetailList).flatMap(Collection::stream).forEach(obj -> obj.setOtcPickingSlipId(newPickingSlip.getId()));

        // 添加新的拣货单
        context.getPickingSlipList().add(newPickingSlip);
    }

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 筛选构建拣货单
     * <p>
     * 根据查询条件筛选工单并构建拣货单，返回库存不足的工单列表。
     * 该方法是筛选构建拣货单流程的入口，主要调用内部的filterBuildWithContext方法。
     * </p>
     *
     * @param query 拣货单筛选构建查询条件
     * @return 库存不足的工单列表
     * <p>
     * TODO: 方法名与功能描述不完全匹配，应明确返回值是库存不足的工单
     * TODO: 考虑添加参数校验逻辑，确保query不为空且包含必要信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WorkOrderNoEnoughAvailQtyVO> filterBuild(OtcPickingSlipFilterBuildQuery query) {
        OtcFilterBuildContextVO context = filterBuildWithContext(query);
        return context.getNoStockWorkOrderList();
    }

    /**
     * 筛选构建拣货单并返回完整上下文
     * <p>
     * 该方法是拣货单构建的核心方法，实现了从工单到拣货单的完整构建流程。
     * 主要流程包括：
     * 1. 初始化构建上下文，设置查询条件
     * 2. 检查并获取符合条件的工单列表
     * 3. 检查工单库存情况，过滤掉库存不足的工单
     * 4. 根据工单类型和其他属性构建拣货单
     * 5. 构建拣货单详情，包括产品、库位和数量信息
     * 6. 根据需要进行拣货单拆分或合并
     * 7. 锁定库位库存，确保拣货过程中不会出现库存冲突
     * 8. 释放工单的库存锁定和预定
     * 9. 批量保存拣货单和拣货单详情
     * 10. 更新包裹、工单和请求单的状态
     * 11. 记录操作日志
     * </p>
     * <p>
     * 该方法在事务中执行，确保所有操作要么全部成功，要么全部失败。
     * 它协调了拣货单、工单、请求单和库存等多个实体的状态变更，
     * 确保数据一致性和业务流程的正确执行。
     * </p>
     *
     * @param query 拣货单筛选构建查询条件，包含筛选条件、构建策略和来源类型
     * @return 构建过程的上下文对象，包含拣货单、工单、请求单及相关状态信息
     * @throws BusinessException 如果构建过程中出现错误，如工单不存在、库存不足或状态更新失败，则抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtcFilterBuildContextVO filterBuildWithContext(OtcPickingSlipFilterBuildQuery query) {
        // 上下文信息
        OtcFilterBuildContextVO context = new OtcFilterBuildContextVO();
        context.setQuery(query);

        // 检查并返回工单列表
        checkAndGetWorkOrderList(context);

        // 检查工单库存
        checkWorkOrderInStock(context);

        if (ObjectUtil.isEmpty(context.getWorkOrderList())) {
            return context;
        }

        // 构建拣货单
        buildPickingSlipList(context);

        // 构建拣货单详情
        buildPickingSlipDetailList(context);

        // 构建之后的处理，切分处理
        afterBuild(context);

        // 锁库位库存
        lockBinLocation(context);

        List<OtcWorkorder> workOrderList = context.getWorkOrderList();
        // 释放WorkOrder LockedInventory
        inventoryLockedService.releaseLockedInventory(otcWorkorderDetailService.findInventoryLockedReleaseLockedParam(workOrderList));
        releaseReserveLocked(context);

        // 批量入库拣货单
        otcPickingSlipService.insertBatch(context.getPickingSlipList());
        // 批量入库拣货单详情
        otcPickingSlipDetailService.insertBatch(context.getPickingSlipDetailList());

        // 分配Package 拣货单id
        List<OtcPackage> packageList = buildUpdatePackageList(workOrderList);
        Validate.isTrue(otcPackageService.updateBatch(packageList) == packageList.size(), "Update Package failed");

        // 更新 WorkOrder 拣货单id 和 InPicking
        List<OtcWorkorder> workorderList = buildUpdateWorkOrderList(workOrderList);
        Validate.isTrue(otcWorkorderService.updateBatch(workorderList) == workorderList.size(), "Update WorkOrder status [InPicking] failed");

        // 更新 Request Processing
        List<OtcRequest> requestList = buildUpdateRequestList(context);
        Validate.isTrue(otcRequestService.updateBatch(requestList) == requestList.size(), "Update Request status [Processing] failed");

        // 记录日志
        recordFilterBuildLog(context);
        return context;
    }

    /**
     * 锁定库位
     *
     * @param context 上下文
     */
    private void lockBinLocation(OtcFilterBuildContextVO context) {
        // 拣货单映射
        Map<Long, OtcPickingSlip> pickingSlipMap = StreamUtils.toMap(context.getPickingSlipList(), IdModel::getId);
        // 构建锁定库位库存
        List<OtcPickingSlipDetail> pickingSlipDetailList = context.getPickingSlipDetailList();
        List<BinLocationDetailLocked> lockedList = pickingSlipDetailList.stream().map(obj -> {
            // 构建锁定 库位详情实体对象
            BinLocationDetailLocked locked = new BinLocationDetailLocked();
            locked.setId(IdWorker.getId());
            locked.setBinLocationDetailId(obj.getBinLocationDetailId());
            locked.setBinLocationId(obj.getBinLocationId());
            locked.setProductId(obj.getProductId());
            locked.setProductVersionId(obj.getProductVersionId());
            locked.setQty(obj.getQty());
            locked.setFinishQty(0);
            locked.setLockedStatus(InventoryLockedStatusEnum.LOCKED.getStatus());
            locked.setRefTableId(obj.getId());
            locked.setRefTableName(OtcPickingSlipDetail.class.getSimpleName());
            locked.setRefTableRefNum(String.valueOf(obj.getLineNum()));
            locked.setRefTableShowName(OtcPickingSlip.class.getSimpleName());
            Optional.ofNullable(pickingSlipMap.get(obj.getOtcPickingSlipId())).ifPresent(slip -> locked.setRefTableShowRefNum(slip.getRefNum()));
            return locked;
        }).toList();

        // 校验库位是否禁用
        BinLocationCheckUtil.checkStatuses(lockedList);

        pickingSlipService.releaseWorkorderVirtualLocked(context.getLockedGroupByWkDetailMap());

        // 锁定库位库存
        binLocationDetailLockedService.lockedBinLocationInventory(lockedList);


        // 填充库位锁信息
        Map<Long, Long> lockedIdMap = lockedList.stream().collect(Collectors.toMap(BinLocationDetailLocked::getRefTableId, IdModel::getId));
        pickingSlipDetailList.forEach(obj -> obj.setBinLocationDetailLockedId(lockedIdMap.get(obj.getId())));
    }

    /**
     * 拣货单构建之后处理逻辑，拆分进货单逻辑
     *
     * @param context 上下文
     */
    private void afterBuild(OtcFilterBuildContextVO context) {
        // // 库位信息,提供给后续使用
        // List<Long> binLocationIdList = StreamUtils.distinctMap(context.getPickingSlipDetailList(), OtcPickingSlipDetail::getBinLocationId);
        // context.setBinLocationMap(binLocationService.binLocationByIds(binLocationIdList));


        // 根据MaxSOSP、MaxSOSPBinLocation、MaxSlapAndGo、MaxSlapAndGoBinLocation, 限制每个分组最大数量，再切分
        // SOSP类型拣货单
        doSplit(context, OtcOrderTypeEnum.SOSP);
        // SOSP类型拣货单
        doSplit(context, OtcOrderTypeEnum.SLAP_AND_GO);

        // 拣货单详情合并处理
        context.setPickingSlipDetailList(aggregateDetailList(context));
    }

    /**
     * 检查并获取工单列表
     *
     * @param context 上下文
     */
    private void checkAndGetWorkOrderList(OtcFilterBuildContextVO context) {
        OtcPickingSlipFilterBuildQuery query = context.getQuery();
        // 构建策略仅Default支持，
        // 后续支持不同策略生成
        OtcPickingSlipBuildStrategyQuery strategy = query.getStrategy();
        boolean isDefault = OtcBuildPickingSlipStrategyEnum.Default.getStatus().equals(strategy.getBuildPickingSlipStrategy());
        if (!isDefault) {
            throw new BusinessException("Only support Default Strategy");
        }

        List<OtcWorkorder> workOrderList = otcWorkorderService.filterBuildByQuery(query);
        if (ObjectUtil.isEmpty(workOrderList)) {
            throw new BusinessException("Current Filter has No WorkOrders to Build!");
        }
        List<Long> workOrderIdList = StreamUtils.distinctMap(workOrderList, OtcWorkorder::getId);
        // 获取工单详情
        Map<Long, List<OtcWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = otcWorkorderDetailService.groupByOtcWorkOrderIdList(workOrderIdList);
        // 让详情数据按照工单顺序一致
        context.setWorkOrderDetailGroupByWorkOrderIdMap(StreamUtils.sortByList(workOrderDetailGroupByWorkOrderIdMap, workOrderList));
        context.setWorkOrderList(new ArrayList<>(workOrderList));

        // Prep工单虚拟库位拣货
        List<OtcPrepWorkorder> prepWorkorderList = otcPrepWorkorderService.listByWorkOrderIdList(workOrderIdList);
        // 绑定到上下文中
        context.setPrepWorkOrderGroupByWkDetailMap(StreamUtils.groupBy(prepWorkorderList, OtcPrepWorkorder::getOtcWorkorderDetailId));
    }

    /**
     * 根据工单PageVO 构建更新工单集合
     *
     * @param workOrderList 工单集合
     * @return /
     */
    private List<OtcWorkorder> buildUpdateWorkOrderList(List<OtcWorkorder> workOrderList) {
        return workOrderList.stream().map(obj -> {
            OtcWorkorder update = new OtcWorkorder();
            update.setId(obj.getId());
            update.setOtcPickingSlipId(obj.getOtcPickingSlipId());
            update.setOtcWorkorderStatus(OtcWorkorderStatusEnum.IN_PICKING.getStatus());
            return update;
        }).toList();
    }

    /**
     * 构建包裹更新对象实体
     *
     * @param workOrderList 工单列表
     * @return 包裹更新对象
     */
    private List<OtcPackage> buildUpdatePackageList(List<OtcWorkorder> workOrderList) {
        List<Long> workOrderIdList = StreamUtils.distinctMap(workOrderList, IdModel::getId);
        // 根据工单id获取包裹列表
        List<OtcPackage> packageList = otcPackageService.listByWorkOrderIdList(workOrderIdList);
        // 工单-拣货单主键映射
        Map<Long, Long> workOrderMap = workOrderList.stream().collect(Collectors.toMap(OtcWorkorder::getId, OtcWorkorder::getOtcPickingSlipId));
        // 赋值拣货单id
        packageList.forEach(pkg -> pkg.setOtcPickingSlipId(workOrderMap.get(pkg.getOtcWorkorderId())));
        return packageList;
    }

    /**
     * 构建拣货单详情
     * 通过工单详情 productId 获取所有 (productVersionId + binLocationId) 构建拣货单详情
     * 通过 拣货单id -> 工单id集合 -> 工单详情集合 -> 产品id集合 -> 库位详情集合 层级构建
     */
    private void buildPickingSlipDetailList(OtcFilterBuildContextVO context) {
        Map<Long, List<OtcWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = context.getWorkOrderDetailGroupByWorkOrderIdMap();
        List<OtcWorkorder> workOrderList = context.getWorkOrderList();

        // 虚拟库位
        context.setVirtualBinLocationMap(binLocationService.allVirtualBinLocationList());
        // 收集拣货单详情和工单的映射关系，后续需要用于拆分拣货单的时候更新工单
        context.setPsDetailIdToWorkOrderMap(new HashMap<>());

        // 工单集合 -> 工单详情集合 -> 产品id集合 -> 库位详情集合 层层嵌套
        List<OtcPickingSlipDetail> pickingSlipDetailList = new ArrayList<>();
        for (OtcWorkorder workorder : workOrderList) {
            if (workOrderDetailGroupByWorkOrderIdMap.containsKey(workorder.getId())) {
                List<OtcWorkorderDetail> details = workOrderDetailGroupByWorkOrderIdMap.get(workorder.getId());
                for (OtcWorkorderDetail detail : details) {
                    List<OtcPickingSlipDetail> builtDetails = doBuildPickingSlipDetail(context, workorder, detail);
                    pickingSlipDetailList.addAll(builtDetails);
                }
            }
        }
        // 库位信息,提供给后续使用
        List<Long> binLocationIdList = StreamUtils.distinctMap(pickingSlipDetailList, OtcPickingSlipDetail::getBinLocationId);
        Map<Long, BinLocationVO> binLocationMap = binLocationService.binLocationByIds(binLocationIdList);

        // 按拣货单ID分组，然后按库位名称排序
        Map<Long, List<OtcPickingSlipDetail>> groupedByPickingSlip = new HashMap<>();
        for (OtcPickingSlipDetail detail : pickingSlipDetailList) {
            groupedByPickingSlip.computeIfAbsent(detail.getOtcPickingSlipId(), k -> new ArrayList<>()).add(detail);
        }

        List<OtcPickingSlipDetail> sortedDetailList = new ArrayList<>();
        for (List<OtcPickingSlipDetail> detailsByPs : groupedByPickingSlip.values()) {
            // 按库位名称排序
            detailsByPs.sort(Comparator.comparing(detail ->
                    binLocationMap.getOrDefault(detail.getBinLocationId(), new BinLocationVO()).getLocationName()));
            sortedDetailList.addAll(detailsByPs);
        }

        // 赋值拣货单详情、工单库位详情到上下文中
        context.setPickingSlipDetailList(sortedDetailList);
        // 同时将排序后的库位映射保存到上下文中，供后续使用
        context.setBinLocationMap(binLocationMap);
    }

    /**
     * 执行构建Prep和正常构建拣货单详情逻辑
     *
     * @param context  上下文
     * @param wk       工单
     * @param wkDetail 工单详情
     * @return /
     */
    @NotNull
    private List<OtcPickingSlipDetail> doBuildPickingSlipDetail(OtcFilterBuildContextVO context, OtcWorkorder wk, OtcWorkorderDetail wkDetail) {

        // 虚拟库位
        Map<Long, BinLocation> virtualBinLocationMap = context.getVirtualBinLocationMap();
        // 收集拣货单详情和工单的映射关系，后续需要用于拆分拣货单的时候更新工单
        Map<Long, OtcWorkorder> workOrderIdAndPickingSlipMap = context.getPsDetailIdToWorkOrderMap();
        // 当前工单详情与Prep工单的映射
        List<OtcPrepWorkorder> currentPrepWorkOrderList = context.getPrepWorkOrderGroupByWkDetailMap().getOrDefault(wkDetail.getId(), Collections.emptyList());

        // 需要从Prep 拣货的数量
        int needPickFromPreBinLocationQty = currentPrepWorkOrderList.stream().map(OtcPrepWorkorder::getPutawayQty).mapToInt(Integer::intValue).sum();
        // 当前Prep工单工作的库位
        List<Long> currentPrepBinLocationIdList = currentPrepWorkOrderList.stream().map(OtcPrepWorkorder::getBinLocationId).distinct().toList();

        // 总数
        AtomicInteger totalQty = new AtomicInteger(wkDetail.getQty());
        // 可以直接取的数量
        AtomicInteger directPickBinLocationQty = new AtomicInteger(Math.max(totalQty.get() - needPickFromPreBinLocationQty, 0));
        AtomicInteger prepPickBinLocationQty = new AtomicInteger(Math.max(totalQty.get() - directPickBinLocationQty.get(), 0));

        List<OtcPickingSlipDetail> psDetailList = new ArrayList<>();
        List<BinLocationDetail> binDetails = context.getRealAvailableBinLocationDetailGroupByProductMap().getOrDefault(wkDetail.getProductId(), Collections.emptyList());
        for (BinLocationDetail binDetail : binDetails) {
            // 还有剩余数量未分配
            if (totalQty.get() <= 0 || binDetail.getInStockQty() <= 0) {
                continue;
            }

            // 当前需要分配数量
            int currentQty;
            // 虚拟库位拣货
            boolean isVirtualBinLocation = virtualBinLocationMap.containsKey(binDetail.getBinLocationId());
            boolean isPrepPick = isVirtualBinLocation && currentPrepBinLocationIdList.contains(binDetail.getBinLocationId());

            // Prep拣货
            if (isPrepPick) {
                currentQty = Math.min(totalQty.get(), binDetail.getInStockQty());
                prepPickBinLocationQty.addAndGet(-currentQty);
            }
            // 正常库位
            else if (!isVirtualBinLocation && directPickBinLocationQty.get() > 0) {
                currentQty = Math.min(totalQty.get(), binDetail.getInStockQty());
                directPickBinLocationQty.addAndGet(-currentQty);
            }
            // 虚拟库位不需要分配
            else {
                continue;
            }

            // 构建拣货单详情
            OtcPickingSlipDetail detail = OtcPickingSlipBuildHelper.buildPickingSlipDetail(wk, wkDetail, binDetail);
            // 设置数量
            detail.setQty(currentQty);
            // 扣除库位上的库存
            binDetail.setInStockQty(binDetail.getInStockQty() - currentQty);
            // 剩余数量
            totalQty.addAndGet(-currentQty);

            // 收集工单和拣货单详情id的映射
            workOrderIdAndPickingSlipMap.put(detail.getId(), wk);

            psDetailList.add(detail);
        }
        // 库存不够
        Validate.isTrue(totalQty.get() == 0, String.format("WorkOrder: %s build PickingSlip details Product: %s Less InStock %d",
                //todo: 这里在Workorder 要实现 toLog
                wk.getRefNum(), Optional.ofNullable(ProductCacheUtil.getById(wkDetail.getProductId())).map(pro -> String.format("%s/%s", pro.getUpc(), pro.getSupplierSku())).orElse(String.valueOf(wkDetail.getProductId())), totalQty.get()));

        // // 记录工单分配到哪些拣货单详情
        // //todo: 这里有问题，这个会导致 pickingSlipDetail 无法从全局角度使用
        // OtcWorkorderBinLocationBO wkBinLocation = new OtcWorkorderBinLocationBO();
        // wkBinLocation.setPsDetailList(psDetailList);
        // wkBinLocation.setWorkorder(wk);
        // context.getWorkorderBinLocationList().add(wkBinLocation);

        return psDetailList;
    }


    /**
     * 检查工单库存
     *
     * @param context 上下文
     */
    private void checkWorkOrderInStock(OtcFilterBuildContextVO context) {
        // 工单详情映射
        Map<Long, List<OtcWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = context.getWorkOrderDetailGroupByWorkOrderIdMap();

        // 工单映射
        Map<Long, OtcWorkorder> workOrderRefNumMap = context.getWorkOrderList().stream().collect(Collectors.toMap(OtcWorkorder::getId, Function.identity()));

        // 处理Prep绑定的虚拟库位
        dealWithVirtualBinLocation(context);

        // 获取不足库存的工单
        List<WorkOrderNoEnoughAvailQtyVO> noEnoughList = pickingSlipService.findNoEnoughList(workOrderDetailGroupByWorkOrderIdMap, context.getRealAvailableBinLocationDetailGroupByProductMap(), productStockMap -> StreamUtils.filterHasProductStock(workOrderDetailGroupByWorkOrderIdMap, productStockMap), (noEnough, detail) -> {
            noEnough.setRefNum(workOrderRefNumMap.get(detail.getOtcWorkorderId()).getRefNum());
            noEnough.setId(detail.getOtcWorkorderId());
        });

        // 设置不足库存的工单到上下文
        context.setNoStockWorkOrderList(noEnoughList);
        // 删除库存不足的工单
        List<Long> noEnoughIdList = StreamUtils.distinctMap(noEnoughList, WorkOrderNoEnoughAvailQtyVO::getId);
        context.getWorkOrderList().removeIf(obj -> noEnoughIdList.contains(obj.getId()));
    }

    /**
     * 处理Prep绑定的虚拟库位
     *
     * @param context 上下文
     */
    private void dealWithVirtualBinLocation(OtcFilterBuildContextVO context) {
        Map<Long, List<OtcWorkorderDetail>> workOrderDetailGroupByWorkOrderIdMap = context.getWorkOrderDetailGroupByWorkOrderIdMap();
        // 获取产品id集合
        List<Long> productIdList = workOrderDetailGroupByWorkOrderIdMap.values().stream().flatMap(Collection::stream).map(OtcWorkorderDetail::getProductId).toList();
        // 虚拟库位获取
        List<Long> workorderDetailIdList = workOrderDetailGroupByWorkOrderIdMap.values().stream().flatMap(Collection::stream).map(IdModel::getId).distinct().toList();
        // 获取锁住的库位详情
        Map<Long, List<BinLocationDetailLocked>> lockedGroupByWkDetailMap = binLocationDetailLockedService.groupByRefTableIdLocked(workorderDetailIdList);

        // 绑定可用库位
        context.setRealAvailableBinLocationDetailGroupByProductMap(pickingSlipService.fullCanBuildVirtualBinLocation(Optional.ofNullable(context.getQuery()).map(OtcPickingSlipFilterBuildQuery::getFilter).map(OtcWorkOrderListQuery::getBinLocationQuery).orElse(null), productIdList, lockedGroupByWkDetailMap));
        context.setLockedGroupByWkDetailMap(lockedGroupByWkDetailMap);

    }

    /**
     * 构建拣货单
     * 1.根据工单 OrderType and PickToStation and transaction_partner_id 分组
     * 2.分组下：根据MaxSOSP、MaxSOSPBinLocation、MaxSlapAndGo、MaxSlapAndGoBinLocation 限制每个分组最大数量，再切分
     *
     * @param context 上下文
     */
    private void buildPickingSlipList(OtcFilterBuildContextVO context) {

        List<OtcWorkorder> workOrderList = context.getWorkOrderList();
        // 根据类型不同分别构建
        Map<String, List<OtcWorkorder>> workOrderGroupMap = workOrderList.stream().collect(Collectors.groupingBy(OtcWorkorder::getOrderType));

        List<OtcPickingSlip> pickingSlipList = new ArrayList<>();
        for (List<OtcWorkorder> workOrders : workOrderGroupMap.values()) {
            // 根据工单类型分别构建
            String orderType = workOrders.get(0).getOrderType();
            switch (OtcOrderTypeEnum.statusOf(orderType)) {
                case SOSP:
                    // SOSP 处理
                    pickingSlipList.addAll(buildPreSosp(workOrders));
                    break;
                case SOMP:
                    // SOMP 处理
                    pickingSlipList.addAll(buildPreSomp(context, workOrders));
                    break;
                case SLAP_AND_GO:
                    // SlapAndGo 处理
                    pickingSlipList.addAll(buildPreSlapAndGo(workOrders));
                    break;
                case MULTI_BOX:
                    // MultiBox 处理
                    pickingSlipList.addAll(buildPreMultiBox(workOrders));
                    break;
                default:
                    throw new BusinessException("Not support order type: " + orderType);
            }
        }
        // 设置构建来源类型
        pickingSlipList.forEach(pickingSlip -> pickingSlip.setBuildFromType(context.getQuery().getBuildFromType().getType()));
        // 赋值拣货单信息到上下文中
        context.setPickingSlipList(new ArrayList<>(pickingSlipList));
    }

    /**
     * SOSP、SlapAndGo相同实现逻辑
     * 按照仓库区域分组，再按照MaxBinLocation、MaxSOSP、MaxSOSPBinLocation限制数量进行再拆分
     *
     * @param context   上下文
     * @param orderType 订单类型
     */
    private void doSplit(OtcFilterBuildContextVO context, OtcOrderTypeEnum orderType) {
        // 需要拆分的拣货单
        List<OtcPickingSlip> pickingSlipsToSplit = context.getPickingSlipList().stream()
                .filter(obj -> orderType.getStatus().equals(obj.getOrderType()))
                .collect(Collectors.toList());

        if (ObjectUtil.isEmpty(pickingSlipsToSplit)) {
            return;
        }

        // 库位信息
        Map<Long, BinLocationVO> binLocationMap = context.getBinLocationMap();

        // 用户拆分限制
        OtcPickingSlipBuildStrategyQuery strategy = context.getQuery().getStrategy();
        int max, maxBinLocation;

        // 根据订单类型设置限制
        if (OtcOrderTypeEnum.SOSP.equals(orderType)) {
            max = strategy.getMaxSosp();
            maxBinLocation = strategy.getMaxSospBinLocation();
        } else if (OtcOrderTypeEnum.SLAP_AND_GO.equals(orderType)) {
            max = strategy.getMaxSlapAndGo();
            maxBinLocation = strategy.getMaxSlapAndGoBinLocation();
        } else {
            return; // 不支持的订单类型
        }

        // 新的拣货单列表，用于替换原有的拣货单
        List<OtcPickingSlip> newPickingSlips = new ArrayList<>();
        // 新的拣货单详情列表，用于替换原有的拣货单详情
        List<OtcPickingSlipDetail> newPickingSlipDetails = new ArrayList<>();

        // 记录需要移除的原始拣货单ID
        Set<Long> originalPickingSlipIdsToRemove = new HashSet<>();

        // 对每个拣货单单独处理
        for (OtcPickingSlip pickingSlip : pickingSlipsToSplit) {
            Long pickingSlipId = pickingSlip.getId();
            originalPickingSlipIdsToRemove.add(pickingSlipId);

            // 获取当前拣货单关联的工单
            List<OtcWorkorder> currentWorkorders = context.getWorkOrderList().stream()
                    .filter(wo -> pickingSlipId.equals(wo.getOtcPickingSlipId()))
                    .collect(Collectors.toList());

            // 获取当前拣货单的详情
            List<OtcPickingSlipDetail> currentDetails = context.getPickingSlipDetailList().stream()
                    .filter(detail -> pickingSlipId.equals(detail.getOtcPickingSlipId()))
                    .collect(Collectors.toList());

            // 按产品ID、库位ID和库位名称分组，并按库位名称排序
            Map<String, List<OtcPickingSlipDetail>> detailGroups = currentDetails.stream()
                    .collect(Collectors.groupingBy(detail -> {
                        BinLocationVO binLocation = binLocationMap.get(detail.getBinLocationId());
                        String locationName = binLocation != null ? binLocation.getLocationName() : "";
                        return String.format("%s:%s:%s", detail.getProductId(), detail.getBinLocationId(), locationName);
                    }));

            // 按库位名称排序
            List<Map.Entry<String, List<OtcPickingSlipDetail>>> sortedGroups = new ArrayList<>(detailGroups.entrySet());
            sortedGroups.sort(Comparator.comparing(entry -> {
                String[] parts = entry.getKey().split(":");
                return parts.length > 2 ? parts[2] : "";
            }));

            // 按仓库区域类型分组的拣货单
            Map<String, List<OtcPickingSlip>> pickingSlipsByZone = new HashMap<>();

            // 处理每个分组的详情
            for (Map.Entry<String, List<OtcPickingSlipDetail>> group : sortedGroups) {
                List<OtcPickingSlipDetail> details = group.getValue();
                OtcPickingSlipDetail firstDetail = details.get(0);

                // 获取库位所属的仓库区域类型
                Long binLocationId = firstDetail.getBinLocationId();
                BinLocationVO binLocation = binLocationMap.get(binLocationId);
                String warehouseZoneType = binLocation != null ? binLocation.getWarehouseZoneType() : "";

                // 获取当前区域的拣货单列表，如果不存在则创建
                List<OtcPickingSlip> currentZonePickingSlips = pickingSlipsByZone.computeIfAbsent(
                        warehouseZoneType, k -> new ArrayList<>());

                // 处理每个详情
                for (OtcPickingSlipDetail detail : details) {
                    boolean isAssigned = false;

                    // 找出与当前详情相关的工单
                    List<OtcWorkorder> relatedWorkorders = currentWorkorders.stream()
                            .filter(x -> x.getOtcPickingSlipId().equals(firstDetail.getOtcPickingSlipId()))
                            .filter(wo -> {
                                List<OtcWorkorderDetail> woDetails = context.getWorkOrderDetailGroupByWorkOrderIdMap().get(wo.getId());
                                return woDetails != null && woDetails.stream()
                                        .anyMatch(woDetail -> woDetail.getProductId().equals(detail.getProductId()));
                            })
                            .limit(detail.getQty()) // 最多取详情数量的工单
                            .collect(Collectors.toList());

                    // 尝试将详情分配到现有的拣货单中
                    for (OtcPickingSlip newPs : currentZonePickingSlips) {
                        // 获取当前拣货单的详情
                        List<OtcPickingSlipDetail> newPsDetails = newPickingSlipDetails.stream()
                                .filter(d -> d.getOtcPickingSlipId().equals(newPs.getId()))
                                .collect(Collectors.toList());

                        // 计算当前拣货单的SKU数量和库位数量
                        int currentSkuCount = (int) newPsDetails.stream()
                                .map(OtcPickingSlipDetail::getProductId)
                                .distinct()
                                .count();
                        int currentBinLocationCount = (int) newPsDetails.stream()
                                .map(OtcPickingSlipDetail::getBinLocationId)
                                .distinct()
                                .count();

                        // 检查是否超过限制
                        if (currentSkuCount >= max || currentBinLocationCount >= maxBinLocation) {
                            continue;
                        }

                        // 创建新的拣货单详情
                        OtcPickingSlipDetail newDetail = new OtcPickingSlipDetail();
                        BeanUtil.copy(detail, newDetail);
                        newDetail.setId(IdWorker.getId());
                        newDetail.setOtcPickingSlipId(newPs.getId());

                        // 更新工单的拣货单ID
                        for (OtcWorkorder workorder : relatedWorkorders) {
                            workorder.setOtcPickingSlipId(newPs.getId());
                        }

                        // 添加到新的拣货单详情列表
                        newPickingSlipDetails.add(newDetail);
                        isAssigned = true;
                        break;
                    }

                    // 如果无法分配到现有拣货单，则创建新的拣货单
                    if (!isAssigned) {
                        // 创建新的拣货单
                        OtcPickingSlip newPs = new OtcPickingSlip();
                        BeanUtil.copy(pickingSlip, newPs);
                        newPs.setId(IdWorker.getId());
                        newPs.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.OTC_PICKING_SLIP.getCode()));

                        // 创建新的拣货单详情
                        OtcPickingSlipDetail newDetail = new OtcPickingSlipDetail();
                        BeanUtil.copy(detail, newDetail);
                        newDetail.setId(IdWorker.getId());
                        newDetail.setOtcPickingSlipId(newPs.getId());

                        // 更新工单的拣货单ID
                        for (OtcWorkorder workorder : relatedWorkorders) {
                            workorder.setOtcPickingSlipId(newPs.getId());
                        }

                        // 添加到列表
                        currentZonePickingSlips.add(newPs);
                        newPickingSlips.add(newPs);
                        newPickingSlipDetails.add(newDetail);
                    }
                }
            }
        }

        // 从上下文中移除原始拣货单
        List<OtcPickingSlip> updatedPickingSlips = context.getPickingSlipList().stream()
                .filter(ps -> !originalPickingSlipIdsToRemove.contains(ps.getId()))
                .collect(Collectors.toList());

        // 从上下文中移除原始拣货单详情
        List<OtcPickingSlipDetail> updatedDetails = context.getPickingSlipDetailList().stream()
                .filter(detail -> !originalPickingSlipIdsToRemove.contains(detail.getOtcPickingSlipId()))
                .collect(Collectors.toList());

        // 添加新的拣货单和详情
        updatedPickingSlips.addAll(newPickingSlips);
        updatedDetails.addAll(newPickingSlipDetails);

        // 更新上下文
        context.setPickingSlipList(updatedPickingSlips);
        context.setPickingSlipDetailList(updatedDetails);

        // 验证工单与拣货单的关联关系
        validateWorkorderPickingSlipReferences(context);
    }

    /**
     * 验证工单与拣货单的关联关系
     * 确保每个工单引用的拣货单ID都存在于拣货单列表中
     *
     * @param context 上下文
     */
    private void validateWorkorderPickingSlipReferences(OtcFilterBuildContextVO context) {
        // 获取所有拣货单ID
        Set<Long> pickingSlipIds = context.getPickingSlipList().stream()
                .map(OtcPickingSlip::getId)
                .collect(Collectors.toSet());

        // 检查每个工单引用的拣货单ID是否存在
        for (OtcWorkorder workorder : context.getWorkOrderList()) {
            Long pickingSlipId = workorder.getOtcPickingSlipId();
            if (pickingSlipId != null && !pickingSlipIds.contains(pickingSlipId)) {
                // 可以选择抛出异常或者重置工单的拣货单ID
                // workorder.setOtcPickingSlipId(null);
                throw new BusinessException(String.format(
                        "Workorder %s references non-existent picking slip ID: %s",
                        workorder.getRefNum(), pickingSlipId));
            }
        }
    }

    /**
     * 构建SOMP类型拣货单
     *
     * @param workOrderList 工单集合
     * @return /
     */
    private List<OtcPickingSlip> buildPreSomp(OtcFilterBuildContextVO context, List<OtcWorkorder> workOrderList) {
        // 按照Detail 第一个 产品排序
        Map<Long, List<OtcWorkorderDetail>> detailMap = context.getWorkOrderDetailGroupByWorkOrderIdMap();
        // 产品id集合
        List<Long> productIdList = detailMap.values().stream().flatMap(Collection::stream).map(OtcWorkorderDetail::getProductId).distinct().toList();
        Map<Long, BaseProductVO> productMap = productService.baseProductByIds(productIdList);
        // 根据产品供应商Sku排序
        List<OtcWorkorder> sortWorkOrderList = workOrderList.stream().sorted(Comparator.comparing(obj -> {
            Long productId = detailMap.get(obj.getId()).get(0).getProductId();
            return productMap.get(productId).getSupplierSku();
        })).toList();
        // 一个工单一个拣货单
        return sortWorkOrderList.stream().map(obj -> OtcPickingSlipBuildHelper.createPickingSlip(OtcOrderTypeEnum.SOMP, Collections.singletonList(obj))).toList();
    }

    /**
     * 构建SlapAndGo类型拣货单
     *
     * @param workOrderList 工单集合
     * @return /
     */
    private List<OtcPickingSlip> buildPreSlapAndGo(List<OtcWorkorder> workOrderList) {
        // SlapAndGo类型
        Map<String, List<OtcWorkorder>> workOrderGroupMap = buildGroupWorkOrderMap(workOrderList);
        return buildPickingSlipByOrderType(workOrderGroupMap, OtcOrderTypeEnum.SLAP_AND_GO);
    }


    /**
     * 构建MultiBox类型拣货单
     *
     * @param workOrderList 工单集合
     * @return /
     */
    private List<OtcPickingSlip> buildPreMultiBox(List<OtcWorkorder> workOrderList) {
        // MultiBox类型
        Map<String, List<OtcWorkorder>> workOrderGroupMap = buildGroupWorkOrderMap(workOrderList);
        return buildPickingSlipByOrderType(workOrderGroupMap, OtcOrderTypeEnum.MULTI_BOX);
    }

    /**
     * 构建SOSP类型拣货单
     *
     * @param workOrderList workOrderList
     * @return /
     */
    private List<OtcPickingSlip> buildPreSosp(List<OtcWorkorder> workOrderList) {
        // SOSP类型
        Map<String, List<OtcWorkorder>> workOrderGroupMap = buildGroupWorkOrderMap(workOrderList);
        return buildPickingSlipByOrderType(workOrderGroupMap, OtcOrderTypeEnum.SOSP);
    }

    /**
     * PickToStation、transaction_partner_id 、hasCusShipRequire、TransactionPartnerId、workorderPrepType分组，构建拣货单
     *
     * @param workOrderList 工单集合
     * @return /
     */
    private Map<String, List<OtcWorkorder>> buildGroupWorkOrderMap(List<OtcWorkorder> workOrderList) {
        return workOrderList.stream().collect(Collectors.groupingBy(obj -> String.format("%s:%s:%s:%s:%s:%s",
                obj.getPickToStation(),
                obj.getRequestSnapshotTransactionPartnerId(),
                obj.getRequestSnapshotHasCusShipRequire(),
                obj.getWorkorderPrepStatus(),
                obj.getWorkorderProductType(),
                OtcBuildShipPackageEnum.BY_WAREHOUSE.getStatus().equals(obj.getBuildShipPackageType()))));
    }


    /**
     * 根据不同类型构建拣货单
     *
     * @param workOrderGroupMap workOrderGroupMap
     * @param orderType         单类型
     * @return /
     */
    private List<OtcPickingSlip> buildPickingSlipByOrderType(Map<String, List<OtcWorkorder>> workOrderGroupMap, OtcOrderTypeEnum orderType) {
        return workOrderGroupMap.values().stream().map(orders -> OtcPickingSlipBuildHelper.createPickingSlip(orderType, orders)).toList();
    }


    /**
     * 获取需要更新单请求单，设置状态Processing
     *
     * @param context 上下文
     * @return /
     */
    private List<OtcRequest> buildUpdateRequestList(OtcFilterBuildContextVO context) {
        // 工单集合
        List<OtcWorkorder> workOrderList = context.getWorkOrderList();
        List<Long> requestIdList = StreamUtils.distinctMap(workOrderList, OtcWorkorder::getOtcRequestId);
        List<OtcRequest> requestList = otcRequestService.listByIds(requestIdList).stream()
                // 同意的才可以进入到工单
                .filter(obj -> RequestStatusEnum.APPROVED.getStatus().equals(obj.getOtcRequestStatus())).peek(obj -> {
                    obj.setOtcRequestStatus(RequestStatusEnum.PROCESSING.getStatus());
                    obj.setProcessStartTime(TimeUtils.now());
                }).toList();

        // 绑定到上下文中
        context.setRequestList(requestList);
        return requestList;
    }

    /**
     * 释放预定锁
     *
     * @param context 上下文
     */
    private void releaseReserveLocked(OtcFilterBuildContextVO context) {
        List<OtcPrepWorkorder> prepWorkorderList = context.getPrepWorkOrderGroupByWkDetailMap().values().stream().flatMap(Collection::stream).toList();
        if (ObjectUtil.isEmpty(prepWorkorderList)) {
            return;
        }
        Map<Long, List<OtcPrepWorkorder>> prepGroupByDetailWkMap = prepWorkorderList.stream().collect(Collectors.groupingBy(OtcPrepWorkorder::getOtcWorkorderDetailId));
        // 获取工单详情
        List<OtcWorkorderDetail> releaseWorkorderDetailList = context.getWorkOrderDetailGroupByWorkOrderIdMap().values().stream().flatMap(Collection::stream).filter(obj -> prepGroupByDetailWkMap.containsKey(obj.getId())).toList();

        pickingSlipService.releaseReserveLocked(BeanUtil.copyNew(releaseWorkorderDetailList, BaseModelInventoryReserveBO.class));
    }

    /**
     * 记录FilterBuild日志
     *
     * @param context 上下文
     */
    private void recordFilterBuildLog(OtcFilterBuildContextVO context) {
        Map<Long, OtcPickingSlip> pickingSlipMap = context.getPickingSlipList().stream().collect(Collectors.toMap(IdModel::getId, Function.identity()));

        // 工单日志
        for (OtcWorkorder workorder : context.getWorkOrderList()) {
            OtcPickingSlip pickingSlip = pickingSlipMap.get(workorder.getOtcPickingSlipId());

            if (ObjectUtil.isEmpty(pickingSlip)) {
                throw new BusinessException(StringUtil.format("PickingSlip is not found for workorder: {}", workorder.getRefNum()));
            }

            // 工单: Filter Build PickingSlip
            OtcWorkOrderAuditLogHelper.recordLog(workorder, WorkorderLogConstant.FILTER_BUILD_STATUS, pickingSlip.toLogBuild(), null, BaseTypeLogEnum.OPERATION.getType());

            // 工单: New -> InPicking
            OtcWorkOrderAuditLogHelper.recordLog(BeanUtil.copyNew(workorder, OtcWorkorder.class), OtcWorkorderStatusEnum.IN_PICKING.getStatus(), null, null);
        }

        // 拣货单: New  日志
        OtcPickingSlipAuditLogHelper.recordLog(context.getPickingSlipList());

        // 请求单: Processing 日志
        OtcRequestAuditLogHelper.recordLog(context.getRequestList());
    }
}
