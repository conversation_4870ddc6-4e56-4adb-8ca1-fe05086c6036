package cn.need.cloud.biz.service.product;


import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.param.product.create.ProductCreateParam;
import cn.need.cloud.biz.model.param.product.create.ProductVersionCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductVersionUpdateParam;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipMarkRemeasureCartonSizeQuery;
import cn.need.cloud.biz.model.query.product.ProductVersionQuery;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundMeasureVO;
import cn.need.cloud.biz.model.vo.product.ProductVersionVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 产品版本详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface ProductVersionService extends SuperService<ProductVersion> {

    /**
     * 根据参数新增产品版本详情
     *
     * @param createParam 请求创建参数，包含需要插入的产品版本详情的相关信息
     * @return 产品版本详情ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(ProductVersionCreateParam createParam);


    /**
     * 根据参数更新产品版本详情
     *
     * @param updateParam 请求创建参数，包含需要更新的产品版本详情的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(ProductVersionUpdateParam updateParam);

    /**
     * 根据查询条件获取产品版本详情列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品版本详情对象的列表(分页)
     */
    List<ProductVersionVO> listByQuery(ProductVersionQuery query);

    /**
     * 根据查询条件获取产品版本详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个产品版本详情对象的列表(分页)
     */
    PageData<ProductVersionVO> pageByQuery(PageSearch<ProductVersionQuery> search);

    /**
     * 根据ID获取产品版本详情
     *
     * @param id 产品版本详情ID
     * @return 返回产品版本详情VO对象
     */
    ProductVersionVO detailById(Long id);

    /**
     * 根据产品版本详情唯一编码获取产品版本详情
     *
     * @param refNum 产品版本详情唯一编码
     * @return 返回产品版本详情VO对象
     */
    ProductVersionVO detailByRefNum(String refNum);

    /**
     * 根据产品创建参数初始化产品版本信息
     *
     * @param createParam 产品创建参数
     * @param entity      产品实体
     */
    void initProductVersionByProductCreateParam(ProductCreateParam createParam, Product entity);

    /**
     * 检查是否存在指定产品id的产品版本
     *
     * @param productId 产品id
     * @return 如果存在返回true，否则返回false
     */
    boolean existByProductId(Long productId);

    /**
     * 获取版本产品id
     *
     * @param productRefNumList 产品参考编码
     * @param upcList           产品upc
     * @param supplierSkuList   供应商sku
     * @return 版本产品id
     */
    Set<Long> getProductVersionIds(Set<String> productRefNumList, Set<String> upcList, Set<String> supplierSkuList);

    /**
     * 更新产品重新测量状态
     *
     * @param measureVO 测量信息
     */
    void updateMeasureStatus(InboundMeasureVO measureVO);

    /**
     * 根据产品ID获取最新产品版本信息
     *
     * @param productId 产品的ID，用于查找最新版本信息如果ID为null，方法将返回null
     * @return 返回一个ProductVersionVO对象，表示最新产品版本信息如果找不到或输入ID为null，则返回null
     */
    ProductVersionVO getLatestProductVersionByProductId(Long productId);

    /**
     * 根据产品ID列表获取每个产品的最新版本信息
     *
     * @param productIdList 产品ID列表，用于查询每个产品的最新版本信息
     * @return 返回一个Map，键是产品ID，值是对应的产品版本信息（ProductVersionVO）
     */
    Map<Long, ProductVersionVO> getLatestProductVersionByProductIdList(List<Long> productIdList);


    /**
     * 重新计算箱子尺寸
     *
     * @param carton 箱子尺寸
     */
    ProductVersion remeasureCartonSize(OtbPickingSlipMarkRemeasureCartonSizeQuery carton);

    /**
     * /**
     * 通过产品id和产品版本号获取产品版本
     *
     * @param productId  产品id
     * @param versionInt 产品版本号
     * @return /
     */
    ProductVersion findByProductIdAndVersionId(Long productId, Integer versionInt);

    /**
     * 通过产品id获取产品版本集合
     *
     * @param productId 产品id
     * @return /
     */
    List<ProductVersion> findByProductId(Long productId);

    /**
     * 获取最新的产品版本
     *
     * @param productId 产品id
     * @return /
     */
    ProductVersion findLatestProductVersionByProductId(Long productId);

    /**
     * 更新产品标题
     *
     * @param productId 产品productId
     * @param title     产品标题
     */
    void updateTitle(Long productId, String title);

    /**
     * 更新产品的供应商SKU和UPC
     * <p>
     * 该方法会同步更新指定产品的所有版本中的供应商SKU和UPC信息。
     * 确保产品版本数据与主产品数据保持一致。
     * </p>
     *
     * @param productId   产品ID
     * @param supplierSku 新的供应商SKU，如果为null则不更新
     * @param upc         新的UPC码，如果为null则不更新
     */
    void updateSupplierSkuAndUpc(Long productId, String supplierSku, String upc);

    /**
     * 根据产品id获取产品版本集合
     *
     * @param productIdList 产品id集合
     * @return /
     */
    List<ProductVersion> getProductVersionIds(Set<Long> productIdList);


    List<ProductVersionVO> getDimensionChangeProduct(LocalDateTime startLastModificationTime, LocalDateTime endLastModificationTime);
}