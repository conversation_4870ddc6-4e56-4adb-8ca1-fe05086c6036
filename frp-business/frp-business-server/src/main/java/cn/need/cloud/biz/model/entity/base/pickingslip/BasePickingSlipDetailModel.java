package cn.need.cloud.biz.model.entity.base.pickingslip;

import cn.need.framework.common.mybatis.model.SuperModel;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * Prep拣货单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BasePickingSlipDetailModel extends SuperModel {


    /**
     * 行序号
     */
    @TableField
    private Integer lineNum;

    /**
     * 数量
     */
    @TableField
    private Integer qty;

    /**
     * 拣货数量
     */
    @TableField
    private Integer pickedQty;

    /**
     * 库位详情id
     */
    @TableField
    private Long binLocationDetailId;

    /**
     * 库位id
     */
    @TableField
    private Long binLocationId;


    /**
     * 租户id
     */
    @TableField
    private Long tenantId;

    /**
     * 仓库id
     */
    @TableField
    private Long warehouseId;

    /**
     * 产品id
     */
    @TableField
    private Long productId;

    /**
     * 产品版本id
     */
    @TableField
    private Long productVersionId;

    /**
     * 库位详情锁id
     */
    @TableField
    private Long binLocationDetailLockedId;

    /**
     * 已经分配的数量
     */
    @TableField
    private Integer allocateQty;

    /**
     * 备注
     */
    @TableField
    private String note;

}
