package cn.need.cloud.biz.converter.fee;


import cn.need.cloud.biz.client.dto.fee.FeeStorageDTO;
import cn.need.cloud.biz.model.entity.fee.FeeStorage;
import cn.need.cloud.biz.model.vo.fee.FeeStorageVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 费用storage 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeStorageConverter extends AbstractModelConverter<FeeStorage, FeeStorageVO, FeeStorageDTO> {

}
