package cn.need.cloud.biz.service.otc.ship.impl;

import cn.need.cloud.biz.client.constant.enums.otc.OtcShipCarrierTypeEnum;
import cn.need.cloud.biz.converter.otc.OtcShipStationConfigConverter;
import cn.need.cloud.biz.mapper.otc.OtcShipStationConfigMapper;
import cn.need.cloud.biz.model.entity.otc.OtcShipStationConfig;
import cn.need.cloud.biz.model.param.otc.create.OtcShipStationConfigCreateParam;
import cn.need.cloud.biz.model.param.otc.update.OtcShipStationConfigUpdateParam;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipStationConfigQuery;
import cn.need.cloud.biz.model.vo.otc.OtcShipStationConfigVO;
import cn.need.cloud.biz.model.vo.page.OtcShipStationConfigPageVO;
import cn.need.cloud.biz.service.otc.ship.OtcShipStationConfigService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static cn.need.cloud.biz.client.constant.ShipMethodCategoryTypeConstant.GR;
import static cn.need.cloud.biz.client.constant.ShipMethodCategoryTypeConstant.GROUND;
import static cn.need.cloud.biz.client.constant.enums.otc.OtcShipCarrierTypeEnum.*;

/**
 * <p>
 * 快递公司配置 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class OtcShipStationConfigServiceImpl extends SuperServiceImpl<OtcShipStationConfigMapper, OtcShipStationConfig> implements OtcShipStationConfigService {

    private static boolean SHIP_CARRIER_TYPE_FLAG;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(OtcShipStationConfigCreateParam createParam) {
        // 检查传入快递公司配置参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取快递公司配置转换器实例，用于将快递公司配置参数对象转换为实体对象
        OtcShipStationConfigConverter converter = Converters.get(OtcShipStationConfigConverter.class);

        // 将快递公司配置参数对象转换为实体对象并初始化
        OtcShipStationConfig entity = initShipStationConfig(converter.toEntity(createParam));

        // 插入快递公司配置实体对象到数据库
        super.insert(entity);

        // 返回快递公司配置ID
        return entity.getId();
    }


    /**
     * 初始化快递公司配置对象
     * 此方法用于设置快递公司配置对象的必要参数，确保其处于有效状态
     *
     * @param entity 快递公司配置对象，不应为空
     * @return 返回初始化后的快递公司配置
     * @throws BusinessException 如果传入的快递公司配置为空，则抛出此异常
     */
    private OtcShipStationConfig initShipStationConfig(OtcShipStationConfig entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("ShipStationConfig cannot be empty");
        }


        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(OtcShipStationConfigUpdateParam updateParam) {
        // 检查传入快递公司配置参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 获取快递公司配置转换器实例，用于将快递公司配置参数对象转换为实体对象
        OtcShipStationConfigConverter converter = Converters.get(OtcShipStationConfigConverter.class);

        // 将快递公司配置参数对象转换为实体对象
        OtcShipStationConfig entity = converter.toEntity(updateParam);

        // 执行更新快递公司配置操作
        return super.update(entity);

    }

    @Override
    public List<OtcShipStationConfigPageVO> listByQuery(OtcShipStationConfigQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<OtcShipStationConfigPageVO> pageByQuery(PageSearch<OtcShipStationConfigQuery> search) {
        Page<OtcShipStationConfig> page = Conditions.page(search, entityClass);
        List<OtcShipStationConfigPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public OtcShipStationConfigVO detailById(Long id) {
        OtcShipStationConfig entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in ShipStationConfig");
        }
        return buildShipStationConfigVO(entity);
    }


    /**
     * 构建快递公司配置VO对象
     *
     * @param entity 快递公司配置对象
     * @return 返回包含详细信息的快递公司配置VO对象
     */
    private OtcShipStationConfigVO buildShipStationConfigVO(OtcShipStationConfig entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的快递公司配置VO对象
        return Converters.get(OtcShipStationConfigConverter.class).toVO(entity);
    }

    /**
     * 根据ShipCarrier 获取ShipStation
     *
     * @param shipCarrier shipCarrier
     * @param shipMethod  shipMethod
     * @return ShipStation
     */
    @Override
    public String getShipStation(String shipCarrier, String shipMethod) {
        String shipCarrierStr = toShipCarrierTypeStr(shipCarrier);
        //不能转换，则返回ShipCarrier
        if (!SHIP_CARRIER_TYPE_FLAG) {
            return shipCarrierStr;
        }
        //把当前shipCarrier转成枚举类型
        OtcShipCarrierTypeEnum otcShipCarrierTypeEnum = OtcShipCarrierTypeEnum.statusOf(shipCarrierStr);
        //根据shipCarrier和shipMethod获取运营配置
        OtcShipStationConfig shipStationConfig = getByCarrierAndMethod(shipCarrier, shipMethod);
        //判空
        if (ObjectUtil.isNotEmpty(shipStationConfig)) {
            return shipStationConfig.getShipStation();
        }
        String expressSuffix = "_SS";
        switch (otcShipCarrierTypeEnum) {
            //FEDEX
            case FEDEX -> {
                return fedexCase(shipMethod, FEDEX, expressSuffix);
            }
            //UPS
            case UPS -> {
                return fedexCase(shipMethod, UPS, expressSuffix);
            }
            //USPS
            case USPS -> {
                return fedexCase(shipMethod, USPS, expressSuffix);
            }
            //AMAZON
            case AMAZON -> {
                //FEDEX
                if (StringUtil.containsIgnoreCase(shipMethod, FEDEX.getStatus())) {
                    return fedexCase(shipMethod, FEDEX, expressSuffix);
                }

                if (StringUtil.containsIgnoreCase(shipMethod, UPS.getStatus())) {
                    return fedexCase(shipMethod, UPS, expressSuffix);
                }
                //USPS
                if (StringUtil.containsIgnoreCase(shipMethod, USPS.getStatus())) {
                    return fedexCase(shipMethod, USPS, expressSuffix);
                }
                //AMAZON
                return AMAZON.getStatus();
            }
            //上文条件不满足，则抛个异常
            default -> throw new BusinessException("shipCarrier  and shipMethod is error");
        }
    }

    @Override
    public OtcShipStationConfig getByCarrierAndMethod(String shipCarrier, String shipMethod) {
        return lambdaQuery()
                .eq(OtcShipStationConfig::getShipCarrier, shipCarrier)
                .eq(OtcShipStationConfig::getShipMethod, shipMethod)
                .one();
    }

    /**
     * 获取运营载体
     *
     * @param shipCarrier 运营载体
     * @return shipStation
     */
    private String toShipCarrierTypeStr(String shipCarrier) {
        //UPS
        if (StringUtil.containsIgnoreCase(shipCarrier, UPS.getStatus())) {
            SHIP_CARRIER_TYPE_FLAG = Boolean.TRUE;
            return OtcShipCarrierTypeEnum.UPS.getStatus();
        }
        //FEDEX
        if (StringUtil.containsIgnoreCase(shipCarrier, FEDEX.getStatus())) {
            SHIP_CARRIER_TYPE_FLAG = Boolean.TRUE;
            return FEDEX.getStatus();
        }
        //USPS
        if (StringUtil.containsIgnoreCase(shipCarrier, USPS.getStatus())) {
            SHIP_CARRIER_TYPE_FLAG = Boolean.TRUE;
            return USPS.getStatus();
        }
        //AMAZON
        if (StringUtil.containsIgnoreCase(shipCarrier, AMAZON.getStatus())) {
            SHIP_CARRIER_TYPE_FLAG = Boolean.TRUE;
            return AMAZON.getStatus();
        }
        SHIP_CARRIER_TYPE_FLAG = false;
        //返回shipCarrier
        return shipCarrier;
    }

    /**
     * 根据 shipMethod、OtcShipCarrierTypeEnum、expressSuffix获取shipStation
     *
     * @param shipMethod    运营方式
     * @param fedex         运营载体类型
     * @param expressSuffix 后缀
     * @return 运营站
     */
    private String fedexCase(String shipMethod, OtcShipCarrierTypeEnum fedex, String expressSuffix) {
        if (StringUtil.containsIgnoreCase(shipMethod, GROUND) ||
                StringUtil.containsIgnoreCase(shipMethod, GR)) {
            return fedex.getStatus();
        }

        return fedex.getStatus().concat(expressSuffix);
    }
}
