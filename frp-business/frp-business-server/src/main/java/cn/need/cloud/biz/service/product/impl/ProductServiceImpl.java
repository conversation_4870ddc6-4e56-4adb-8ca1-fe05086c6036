package cn.need.cloud.biz.service.product.impl;

import cn.need.cloud.biz.cache.ProductCacheRepertory;
import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.ProductStatusFieldConstant;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrepWordorderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductGroupTypeEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductLogStatusEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductTypeEnum;
import cn.need.cloud.biz.converter.product.ProductConverter;
import cn.need.cloud.biz.mapper.product.ProductMapper;
import cn.need.cloud.biz.model.entity.product.*;
import cn.need.cloud.biz.model.param.product.create.ProductCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductQuery;
import cn.need.cloud.biz.model.query.product.ProductTreeQuery;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.base.product.*;
import cn.need.cloud.biz.model.vo.log.ModificationLogVO;
import cn.need.cloud.biz.model.vo.product.ProductVO;
import cn.need.cloud.biz.model.vo.product.UpcCountVO;
import cn.need.cloud.biz.service.helper.auditshowlog.product.ProductAuditLogHelper;
import cn.need.cloud.biz.service.product.*;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.ModifyCompareUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.api.NumberGenerateClient;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.util.ApiUtil;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品服务实现类
 * </p>
 * <p>
 * 该类实现了产品相关的核心业务逻辑，包括产品的创建、查询、更新及各种产品类型（普通产品、组合产品、多箱产品等）的管理。
 * 提供了全面的产品生命周期管理，包括产品信息校验、产品版本管理、产品类型转换等功能。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 产品基础信息的增删改查
 * 2. 产品UPC和SKU的唯一性校验
 * 3. 产品类型管理（普通产品、组合产品、多箱产品）
 * 4. 产品版本控制
 * 5. 产品缓存管理
 * 6. 产品审计日志记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class ProductServiceImpl extends SuperServiceImpl<ProductMapper, Product> implements ProductService {

    /**
     * 编号生成客户端，用于生成产品的唯一编号
     * <p>
     * 负责生成符合业务规则的产品编号，确保系统中产品编号的唯一性和规范性。
     * 通过该客户端可以获取不同类型的编号，如产品编号、订单编号等。
     * </p>
     */
    @Resource
    private NumberGenerateClient numberGenerateClient;

    /**
     * 产品版本服务，用于管理产品的不同版本
     * <p>
     * 负责产品版本的创建、更新和查询，支持产品信息的版本控制，
     * 便于追踪产品信息的变更历史，保证数据的一致性和可追溯性。
     * </p>
     */
    @Resource
    private ProductVersionService productVersionService;

    /**
     * 产品多箱服务，使用懒加载避免循环依赖
     * <p>
     * 负责管理产品的多箱信息，包括多箱产品的创建、更新和查询。
     * 使用@Lazy注解避免与其他服务之间的循环依赖问题。
     * </p>
     */
    @Resource
    @Lazy
    private ProductMultiboxService productMultiboxService;

    /**
     * 产品多箱详情服务
     * <p>
     * 负责管理产品多箱的详细信息，如多箱中包含的具体产品、数量等。
     * 提供多箱详情的增删改查功能。
     * </p>
     */
    @Resource
    private ProductMultiboxDetailService productMultiboxDetailService;

    /**
     * 产品组服务，使用懒加载避免循环依赖
     * <p>
     * 负责管理产品组信息，包括产品组的创建、更新和查询。
     * 产品组用于表示产品之间的组合关系，如父子产品关系。
     * 使用@Lazy注解避免与其他服务之间的循环依赖问题。
     * </p>
     */
    @Resource
    @Lazy
    private ProductGroupService productGroupService;

    /**
     * 产品组件服务，使用懒加载避免循环依赖
     * <p>
     * 负责管理产品组件信息，包括组件的创建、更新和查询。
     * 产品组件用于表示产品的组成部分，如一个产品由多个组件组成。
     * 使用@Lazy注解避免与其他服务之间的循环依赖问题。
     * </p>
     */
    @Resource
    @Lazy
    private ProductComponentService productComponentService;

    /**
     * 租户缓存服务，用于获取租户相关信息
     * <p>
     * 负责缓存和提供租户信息，提高系统性能，减少数据库访问。
     * 通过该服务可以快速获取租户的基本信息，如名称、编码等。
     * </p>
     */
    @Resource
    private TenantCacheService tenantCacheService;

    /**
     * 产品缓存仓库，用于缓存频繁访问的产品信息
     * <p>
     * 负责缓存产品信息，提高系统性能，减少数据库访问。
     * 缓存策略包括产品基本信息、产品属性等，支持按ID、编码等多种方式查询。
     * </p>
     */
    @Resource
    private ProductCacheRepertory productCacheRepertory;

    /**
     * 产品扫描服务，用于处理产品扫描相关功能
     * <p>
     * 负责处理产品扫描相关的业务逻辑，如条码扫描、产品识别等。
     * 提供产品扫描数据的初始化、更新和查询功能。
     * </p>
     */
    @Resource
    private ProductScanService productScanService;


    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 根据参数创建新产品
     * <p>
     * 该方法在事务中执行，确保数据一致性。创建产品时会进行一系列验证：
     * - 检查UPC和SKU是否为空
     * - 检查UPC在同一合作伙伴下是否唯一
     * - 检查SKU在同一合作伙伴下是否唯一
     * - 检查UPC和SKU不能相同
     * </p>
     * <p>
     * 创建过程包括：
     * 1. 初始化产品基本信息
     * 2. 插入产品记录
     * 3. 初始化产品扫描数据
     * 4. 创建初始产品版本
     * 5. 记录审计日志
     * </p>
     *
     * @param createParam 产品创建参数，包含产品的基本信息
     * @return 创建的产品ID
     * @throws BusinessException 如果参数为空或验证失败，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       TODO: 考虑添加事务回滚监控和异常处理细化
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       优化建议：添加更详细的异常捕获和日志记录，以便更好地追踪问题
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       示例优化代码：
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       <pre>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       @Transactional(rollbackFor = Exception.class)
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       public Long insertByParam(ProductCreateParam createParam) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           try {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               // 参数校验
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               if (ObjectUtil.isEmpty(createParam)) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   log.error("创建产品失败：参数为空");
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   throw new BusinessException("参数不能为空");
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               }
     *
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               // UPC和SKU校验
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               if (ObjectUtil.isEmpty(createParam.getUpc()) || ObjectUtil.isEmpty(createParam.getSupplierSku())) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   log.error("创建产品失败：UPC或SupplierSKU为空, 参数: {}", JsonUtil.toJson(createParam));
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   throw new BusinessException("UPC和SupplierSKU不能为空");
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               }
     *
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               // 其他业务逻辑...
     *
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               // 记录成功日志
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               log.info("产品创建成功, ID: {}, UPC: {}, SKU: {}",
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       entity.getId(), entity.getUpc(), entity.getSupplierSku());
     *
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               return entity.getId();
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           } catch (BusinessException be) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               // 业务异常直接抛出
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               throw be;
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           } catch (Exception e) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               // 其他异常转换为业务异常并记录日志
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               log.error("创建产品失败，参数: {}, 异常: {}",
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       JsonUtil.toJson(createParam), e.getMessage(), e);
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               throw new BusinessException("创建产品失败：" + e.getMessage());
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           }
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       }
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       </pre>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Product insertByParam(ProductCreateParam createParam) {
        // 检查传入产品参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        // UPC 可以为空
        // if (ObjectUtil.isEmpty(createParam.getUpc())) {
        //     throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "Upc"));
        // }
        if (ObjectUtil.isEmpty(createParam.getSupplierSku())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "SupplierSku"));
        }

        //校验upc(同一个partner下)全局唯一
        checkGlobalUniqueUpc(createParam.getUpc(), createParam.getTransactionPartnerId());

        //校验sku(同一个partner下)全局唯一
        checkGlobalUniqueSku(createParam.getSupplierSku(), createParam.getTransactionPartnerId());

        // 校验UPC不能和SKU相同
        if (StringUtil.equals(createParam.getUpc(), createParam.getSupplierSku())) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "UPC and SupplierSKU cannot be the same value"));
        }

        // 获取产品转换器实例，用于将产品参数对象转换为实体对象
        ProductConverter converter = Converters.get(ProductConverter.class);

        // 将产品参数对象转换为实体对象并初始化
        Product entity = initProduct(converter.toEntity(createParam));

        // 插入产品实体对象到数据库
        super.insert(entity);

        // 初始化新增产品扫描数据
        productScanService.initInsertScanByProduct(entity);

        // 初始化新增一个产品版本
        productVersionService.initProductVersionByProductCreateParam(createParam, entity);

        // 记录到log日志
        ProductAuditLogHelper.recordLog(
                entity,
                ProductLogStatusEnum.INIT.getStatus(),
                BaseTypeLogEnum.BASE_INFO.getType(),
                entity.toLog()
        );

        // 返回entity
        return entity;
    }

    /**
     * 校验UPC在同一合作伙伴下的唯一性
     * <p>
     * 该方法检查指定的UPC是否在同一合作伙伴和租户下已存在。
     * 如果UPC已存在，则抛出业务异常。
     * </p>
     *
     * @param upc                  需要校验的UPC编码
     * @param transactionPartnerId 交易合作伙伴ID
     * @throws BusinessException 如果UPC或交易合作伙伴ID为空，或UPC已存在，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       TODO: 方法的返回逻辑看起来有些反直觉，如果返回0表示已存在则抛出异常
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       优化建议：修改数据库查询方法或此方法的返回值约定，使其更直观
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       示例优化代码：
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       <pre>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       public void checkGlobalUniqueUpc(String upc, Long transactionPartnerId) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           if (ObjectUtil.isEmpty(upc)) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "Upc"));
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           }
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           if (ObjectUtil.isEmpty(transactionPartnerId)) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "TransactionPartnerId"));
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           }
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           //查询租户
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Long tenantId = TenantContextHolder.getTenantId();
     *
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           // 修改为返回是否存在的布尔值，使逻辑更清晰
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           boolean exists = mapper.isUpcExists(upc, transactionPartnerId, tenantId);
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           if (exists) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               throw new BusinessException(String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "Product", "Upc", upc));
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           }
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       }
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       </pre>
     */
    @Override
    public void checkGlobalUniqueUpc(String upc, Long transactionPartnerId) {
        if (ObjectUtil.isEmpty(upc)) {
            //throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "Upc"));
            return;
        }
        if (ObjectUtil.isEmpty(transactionPartnerId)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "TransactionPartnerId"));
        }
        //查询租户
        Long tenantId = TenantContextHolder.getTenantId();

        Integer res = mapper.checkGlobalUniqueUpc(upc, transactionPartnerId, tenantId);
        if (res == 0) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "Product", "UPC", upc));
        }
    }

    /**
     * 批量校验UPC列表的唯一性
     * <p>
     * 该方法检查指定的UPC列表是否在产品表和多箱表中已存在。
     * 如果任何UPC已存在，则抛出业务异常。
     * </p>
     *
     * @param upcList              需要校验的UPC列表
     * @param transactionPartnerId 交易合作伙伴ID
     * @throws BusinessException 如果任何UPC已存在于产品表或多箱表中，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       TODO: 这个方法可能会有性能问题，当产品数量很多时，内存中的比较可能会很慢
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       优化建议：考虑使用数据库查询直接检查UPC列表是否存在，而不是先查询所有产品
     */
    @Override
    public void checkUniqueUpcByList(Set<String> upcList, Long transactionPartnerId) {
        //查询产品并转换成upc set
        List<Product> productList = lambdaQuery().eq(Product::getTransactionPartnerId, transactionPartnerId).list();
        Set<String> productUpc = productList.stream().map(Product::getUpc).collect(Collectors.toSet());
        //查询多箱并转换成upc set
        List<ProductMultibox> multiboxList = productMultiboxService.getListByPartnerId(transactionPartnerId);
        Set<String> multiboxUpc = multiboxList.stream().map(ProductMultibox::getUpc).collect(Collectors.toSet());
        //校验重复
        if (upcList.stream().anyMatch(productUpc::contains)) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Duplicate UPC found in product list"));
        }
        if (upcList.stream().anyMatch(multiboxUpc::contains)) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Duplicate UPC found in multibox list"));
        }
    }

    /**
     * 根据产品ID列表获取基础产品视图对象映射
     * <p>
     * 该方法从缓存中获取指定ID列表的产品信息，转换为基础产品视图对象，并返回ID到视图对象的映射。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 产品ID到基础产品视图对象的映射，如果ID列表为空则返回空映射
     */
    @Override
    public Map<Long, BaseProductVO> baseProductByIds(List<Long> productIds) {
        if (ObjectUtil.isEmpty(productIds)) {
            return Collections.emptyMap();
        }
        // 缓存获取
        List<ProductCache> productList = ProductCacheUtil.listByIds(productIds);
        return StreamUtils.toMap(BeanUtil.copyNew(productList, BaseProductVO.class), BaseProductVO::getId);
    }

    /**
     * 根据产品ID获取基础产品视图对象
     * <p>
     * 该方法通过调用baseProductByIds方法获取单个产品的基础视图对象。
     * </p>
     *
     * @param id 产品ID
     * @return 基础产品视图对象，如果不存在则返回null
     */
    @Override
    public BaseProductVO baseProductById(Long id) {
        Map<Long, BaseProductVO> productMap = baseProductByIds(Collections.singletonList(id));
        return ObjectUtil.isEmpty(productMap) ? null : productMap.get(id);
    }

    /**
     * 校验同一合作伙伴下SKU的唯一性
     * <p>
     * 该方法检查指定合作伙伴下所有产品的SKU是否唯一。
     * 如果存在重复的SKU，则抛出业务异常。
     * </p>
     *
     * @param transactionPartnerId 交易合作伙伴ID
     * @throws BusinessException 如果同一合作伙伴下存在重复的SKU，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       TODO: 该方法会一次性加载所有产品到内存，当产品数量很大时可能会有性能问题
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       优化建议：考虑使用数据库查询直接检查重复的SKU，而不是在内存中比较
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       示例优化代码：
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       <pre>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       public void checkInitGlobalUniqueSku(Long transactionPartnerId) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Integer duplicateCount = mapper.countDuplicateSku(transactionPartnerId);
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           if (duplicateCount > 0) {
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               throw new BusinessException("Sku is not unique in same partner");
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           }
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       }
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       </pre>
     */
    public void checkInitGlobalUniqueSku(Long transactionPartnerId) {
        // 查询交易伙伴ID等于给定值且未被移除的产品，并提取其供应商SKU
        List<String> list = getListByTransactionPartnerId(transactionPartnerId).stream().map(Product::getSupplierSku).toList();

        // 使用HashSet去除重复的SKU，确保每个SKU都是唯一的
        HashSet<String> skuSet = new HashSet<>(list);

        // 检查列表是否不为空且SKU是否重复，如果SKU在同一个交易伙伴中不是唯一的，则抛出异常
        if (ObjectUtil.isNotEmpty(list) && skuSet.size() != list.size()) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Duplicate SKU found in the same partner"));
        }
    }

    /**
     * 根据交易合作伙伴ID获取产品列表
     * <p>
     * 该方法查询指定交易合作伙伴ID下的所有产品。
     * </p>
     *
     * @param transactionPartnerId 交易合作伙伴ID
     * @return 产品列表
     */
    @Override
    public List<Product> getListByTransactionPartnerId(Long transactionPartnerId) {
        return lambdaQuery().eq(Product::getTransactionPartnerId, transactionPartnerId)
                .list();
    }

    /**
     * 校验同一合作伙伴下UPC的全局唯一性
     * <p>
     * 该方法通过查询数据库检查指定合作伙伴和租户下UPC是否在不同表中出现重复。
     * 如果存在重复的UPC，则记录错误日志并抛出业务异常。
     * </p>
     *
     * @param transactionPartnerId 交易合作伙伴ID
     * @throws BusinessException 如果同一合作伙伴下存在重复的UPC，则抛出业务异常
     */
    public void checkInitGlobalUniqueUpc(Long transactionPartnerId) {
        Long tenantId = TenantContextHolder.getTenantId();
        List<UpcCountVO> upcList = mapper.checkInitGlobalUniqueUpc(transactionPartnerId, tenantId);
        if (!upcList.isEmpty()) {
            log.error("Upc is not unique in tables,{}", upcList);
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "Product", "Upc", "in same partner"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Product updateByParam(ProductUpdateParam updateParam) {
        // 检查传入产品参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        Product oldProduct = super.getById(updateParam.getId());
        if (ObjectUtil.isEmpty(oldProduct)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "Product", updateParam.getId()));
        }

        // 校验UPC和SKU的唯一性（如果有更新）
        if (ObjectUtil.isNotEmpty(updateParam.getUpc()) && !StringUtil.equals(oldProduct.getUpc(), updateParam.getUpc())) {
            checkGlobalUniqueUpc(updateParam.getUpc(), oldProduct.getTransactionPartnerId());
            ProductAuditLogHelper.recordLog(
                    oldProduct,
                    "UPC Change",
                    BaseTypeLogEnum.BASE_INFO.getType(),
                    StringUtil.format("Old UPC: {}, New UPC: {}", oldProduct.getUpc(), updateParam.getUpc())
            );
        }
        if (ObjectUtil.isNotEmpty(updateParam.getSupplierSku()) && !StringUtil.equals(oldProduct.getSupplierSku(), updateParam.getSupplierSku())) {
            checkGlobalUniqueSku(updateParam.getSupplierSku(), oldProduct.getTransactionPartnerId());
            ProductAuditLogHelper.recordLog(
                    oldProduct,
                    "SKU Change",
                    BaseTypeLogEnum.BASE_INFO.getType(),
                    StringUtil.format("Old SKU: {}, New SKU: {}", oldProduct.getSupplierSku(), updateParam.getSupplierSku())
            );
        }

        // 校验UPC不能和SKU相同
        String newUpc = ObjectUtil.isNotEmpty(updateParam.getUpc()) ? updateParam.getUpc() : oldProduct.getUpc();
        String newSupplierSku = ObjectUtil.isNotEmpty(updateParam.getSupplierSku()) ? updateParam.getSupplierSku() : oldProduct.getSupplierSku();
        if (ObjectUtil.isNotEmpty(newUpc) && StringUtil.equals(newUpc, newSupplierSku)) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "UPC and SupplierSKU cannot be the same value"));
        }

        // 获取产品转换器实例，用于将产品参数对象转换为实体对象
        ProductConverter converter = Converters.get(ProductConverter.class);

        // 将产品参数对象转换为实体对象
        Product entity = converter.toEntity(updateParam);
        int update = super.update(entity);

        //如果产品更改了Title 同步更新产品版本的Title
        if (ObjectUtil.isNotEmpty(updateParam.getTitle())) {
            productVersionService.updateTitle(updateParam.getId(), updateParam.getTitle());
        }

        // 如果更新了supplierSku或upc，同步更新所有版本和扫描表数据
        if (ObjectUtil.isNotEmpty(updateParam.getSupplierSku()) || ObjectUtil.isNotEmpty(updateParam.getUpc())) {
            // 同步更新产品版本表中的supplierSku和upc
            productVersionService.updateSupplierSkuAndUpc(updateParam.getId(), updateParam.getSupplierSku(), updateParam.getUpc());

            // 同步更新扫描表中的数据
            productScanService.updateScanDataForProduct(
                    updateParam.getId(),
                    oldProduct.getSupplierSku(),
                    updateParam.getSupplierSku(),
                    oldProduct.getUpc(),
                    updateParam.getUpc()
            );
        }

        //获取新的产品
        Product newProduct = super.getById(updateParam.getId());
        // 记录到log日志
        ProductAuditLogHelper.recordLog(
                newProduct,
                ProductLogStatusEnum.CHANGE.getStatus(),
                BaseTypeLogEnum.BASE_INFO.getType(),
                ModifyCompareUtil.recordModifyLog(newProduct, oldProduct)
        );

        //清理缓存
        productCacheRepertory.delProduct(updateParam.getId().toString());
        // 执行更新产品操作
        return newProduct;
    }


    @Override
    public List<ProductVO> listByQuery(ProductQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<ProductVO> pageByQuery(PageSearch<ProductQuery> search) {
        Page<Product> page = Conditions.page(search, entityClass);
        List<ProductVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public ProductVO detailById(Long id) {
        Product entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in Product");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "Product", id));
        }

        ProductVO productVO = buildProductVO(entity);
        if (ObjectUtil.isEmpty(productVO)) {
            throw new BusinessException("productVO is empty");
        }
        return productVO;
    }

    @Override
    public ProductVO detailByRefNum(String refNum) {
        Product entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in Product");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "Product", "refNum", refNum));
        }
        return buildProductVO(entity);
    }


    @Override
    public PrepFullProductVO getPrepProductDescription(Long productId, Integer productVersionInt, String prepWorkOrderType) {
        ProductTreeQuery productTreeQuery = new ProductTreeQuery()
                .setProductId(productId)
                .setProductVersionInt(productVersionInt)
                .setPrepWorkOrderType(prepWorkOrderType);
        return getPrepProductTree(productTreeQuery);
    }

    @Override
    public PrepFullProductVO getPrepProductTree(ProductTreeQuery productTreeQuery) {
        if (StringUtil.isBlank(productTreeQuery.getPrepWorkOrderType())) {
            throw new BusinessException("prepWorkOrderType cannot be empty");
        }
        if (ObjectUtil.isNull(productTreeQuery.getProductVersionInt())) {
            throw new BusinessException("productVersionInt cannot be null");
        }
        PrepWordorderTypeEnum type = PrepWordorderTypeEnum.typeOf(productTreeQuery.getPrepWorkOrderType());
        if (ObjectUtil.isNull(type)) {
            throw new BusinessException(String.format("Unsupported preprocessing ticket type: %s", productTreeQuery.getPrepWorkOrderType()));
        }
        // 根据类型获取不同信息
        Long productId = productTreeQuery.getProductId();
        Integer productVersionInt = productTreeQuery.getProductVersionInt();
        String prepWorkOrderType = productTreeQuery.getPrepWorkOrderType();
        // 根据类型获取不同信息
        PrepFullProductVO prepFullProduct;
        switch (type) {
            // 获取组件信息
            case PREP_PACK, PREP_CONVERT_PACK -> prepFullProduct = getPrepPackProduct(productId, productVersionInt);
            // 获取MultiBox信息
            case PREP_MULTI_BOX, PREP_CONVERT_MULTI_BOX ->
                    prepFullProduct = getPrepMultiBoxProduct(productId, productVersionInt);
            // 获取Convert信息
            case PREP_CONVERT -> prepFullProduct = getPrepConvertProduct(productId, productVersionInt);
            default ->
                    throw new BusinessException(String.format("Unsupported preprocessing ticket type: %s", prepWorkOrderType));
        }
        prepFullProduct.setType(type);
        prepFullProduct.setProductVersionInt(productVersionInt);
        return prepFullProduct;
    }

    @Override
    public Product getBySupplierSku(String supplierSku, Long transactionPartnerId) {
        return lambdaQuery()
                .eq(Product::getSupplierSku, supplierSku)
                .eq(Product::getTransactionPartnerId, transactionPartnerId)
                .one();
    }

    @Override
    public Product get(String refNum, String supplierSku, Long transactionPartnerId) {
        return lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(refNum), Product::getRefNum, refNum)
                .eq(ObjectUtil.isNotEmpty(supplierSku), Product::getSupplierSku, supplierSku)
                .eq(ObjectUtil.isNotEmpty(transactionPartnerId), Product::getTransactionPartnerId, transactionPartnerId)
                .one();
    }

    @Override
    public void updateProductType(Long productId, ProductTypeEnum productTypeEnum) {
        // 获取产品信息
        Product product = super.getById(productId);

        // 更新产品危险类型
        ProductCache cache = ProductCacheUtil.getById(productId);
        cache.setProductType(productTypeEnum.getType());
        product.setProductType(productTypeEnum.getType());

        //持久化数据库
        super.update(product);

        //记录日志
        ProductAuditLogHelper.recordLog(
                product,
                ProductLogStatusEnum.CHANGE.getStatus(),
                BaseTypeLogEnum.HAZMAT.getType()
        );

        //更新缓存
        productCacheRepertory.addProduct(cache);
    }

    @Override
    public boolean isGroup(Long id) {
        return ProductCacheUtil.getById(id).getGroupType().equals(ProductGroupTypeEnum.PARENT.getType()) || ProductCacheUtil.getById(id).getGroupType().equals(ProductGroupTypeEnum.CHILD.getType());
    }

    @Override
    public boolean isAssembly(Long id) {
        return ProductCacheUtil.getById(id).getAssemblyProductFlag();
    }


    @Override
    public boolean isMultibox(Long id) {
        return ProductCacheUtil.getById(id).getMultiboxFlag();
    }

    @Override
    public boolean isChildGroup(Long id) {
        return ProductCacheUtil.getById(id).getGroupType().equals(ProductGroupTypeEnum.CHILD.getType());
    }

    @Override
    public boolean isAllProductInPartnerId(Long transactionPartnerId, List<Long> idList) {
        // 获取当前租户ID
        Long tenantId = TenantContextHolder.getTenantId();
        if (ObjectUtil.isEmpty(tenantId)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "TenantId"));
        }
        // 检查给定ID列表中的所有产品是否都属于当前租户且交易伙伴ID匹配
        return ProductCacheUtil.listByIds(idList).stream()
                .allMatch(productCache -> productCache.getTenantId().equals(tenantId)
                        && productCache.getTransactionPartnerId().equals(transactionPartnerId)
                );
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAssemblyFlagById(Long id, Boolean flag) {
        //构建product 对象
        Product product = new Product();
        product.setId(id);
        product.setAssemblyProductFlag(flag);
        Boolean oldFlag = ProductCacheUtil.getById(id).getAssemblyProductFlag();
        super.update(product);
        //刷新缓存
        productCacheRepertory.delProduct(String.valueOf(id));

        Product newProduct = BeanUtil.copyNew(ProductCacheUtil.getById(id), Product.class);

        //记录log日志
        ProductAuditLogHelper.recordLog(
                newProduct,
                ProductLogStatusEnum.CHANGE.getStatus(),
                BaseTypeLogEnum.ASSEMBLY.getType(),
                JsonUtil.toJson(new ModificationLogVO(ProductStatusFieldConstant.ASSEMBLY_PRODUCT_FLAG, flag.toString(), oldFlag.toString()))
        );
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMultiboxFlagById(Long id, Boolean flag) {
        //构建product 对象
        Product product = new Product();
        product.setId(id);
        // 设置多箱标志
        product.setMultiboxFlag(flag);
        // 从缓存中获取原始的多箱标志
        Boolean oldFlag = ProductCacheUtil.getById(id).getMultiboxFlag();
        // 更新数据库中的产品信息
        super.update(product);
        //刷新缓存
        productCacheRepertory.delProduct(String.valueOf(id));

        // 从缓存中获取更新后的商品信息并复制到新对象
        Product newProduct = BeanUtil.copyNew(ProductCacheUtil.getById(id), Product.class);

        //记录log日志
        ProductAuditLogHelper.recordLog(
                newProduct,
                ProductLogStatusEnum.CHANGE.getStatus(),
                BaseTypeLogEnum.MULTI_BOX.getType(),
                JsonUtil.toJson(new ModificationLogVO(ProductStatusFieldConstant.MULTIBOX_FLAG, flag.toString(), oldFlag.toString()))
        );
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGroupTypeByIdList(List<Long> idList, String type) {
        // 根据ID列表转换生成产品对象列表
        List<Product> list = idList.stream().map(o -> {
            Product product = new Product();
            product.setId(o);
            product.setGroupType(type);
            return product;
        }).toList();

        // 获取原始产品分组类型
        String oldType = ProductCacheUtil.getById(idList.get(NumberUtils.INTEGER_ZERO)).getGroupType();

        // 批量更新产品信息
        super.updateBatch(list);
        // 删除缓存
        productCacheRepertory.delProduct(idList.stream().map(String::valueOf).distinct().toList());

        // 复制并转换缓存中的产品信息列表
        List<Product> productList = BeanUtil.copyNew(ProductCacheUtil.listByIds(idList), Product.class);

        // 通过流操作处理每个产品，生成审计日志集合
        productList.forEach(item ->
                ProductAuditLogHelper.recordLog(
                        item,
                        ProductLogStatusEnum.CHANGE.getStatus(),
                        BaseTypeLogEnum.GROUP.getType(),
                        JsonUtil.toJson(new ModificationLogVO(ProductStatusFieldConstant.GROUP_TYPE, oldType, type))
                )
        );
    }


    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 校验sku(同一个partner下)全局唯一
     *
     * @param supplierSku          sku
     * @param transactionPartnerId 交易伙伴id
     */
    private void checkGlobalUniqueSku(String supplierSku, Long transactionPartnerId) {
        if (ObjectUtil.isEmpty(supplierSku)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "SupplierSku"));
        }
        if (ObjectUtil.isEmpty(transactionPartnerId)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "TransactionPartnerId"));
        }
        //校验原数据库中的sku是否唯一
        // checkInitGlobalUniqueSku(transactionPartnerId);

        //查询租户
        Long tenantId = TenantContextHolder.getTenantId();

        Integer res = mapper.checkGlobalUniqueSku(supplierSku, transactionPartnerId, tenantId);
        if (res == 0) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_UNIQUE_CONSTRAINT, "Product", "SupplierSku", supplierSku));
        }
    }

    /**
     * 初始化产品对象
     * 此方法用于设置产品对象的必要参数，确保其处于有效状态
     *
     * @param entity 产品对象，不应为空
     * @return 返回初始化后的产品
     * @throws BusinessException 如果传入的产品为空，则抛出此异常
     */
    private Product initProduct(Product entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "Product"));
        }
        // 取得到租户简称 并转大写
        String abbrName = tenantCacheService.getById(entity.getTransactionPartnerId()).getAbbrName().toUpperCase();

        // 生成RefNum
        entity.setRefNum(ApiUtil.getResultData(numberGenerateClient.generateNumberWithProduct(RefNumTypeEnum.PRODUCT.getCode(), abbrName)));
        //设置初始状态
        entity.setAssemblyProductFlag(Boolean.FALSE);
        entity.setMultiboxFlag(Boolean.FALSE);
        entity.setGroupType(ProductGroupTypeEnum.NONE.getType());
        entity.setProductType(ProductTypeEnum.Normal.getType());
        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建产品VO对象
     *
     * @param entity 产品对象
     * @return 返回包含详细信息的产品VO对象
     */
    private ProductVO buildProductVO(Product entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的产品VO对象
        return Converters.get(ProductConverter.class).toVO(entity);
    }

    /**
     * 获取MultiBox产品信息
     *
     * @param productId  产品id
     * @param versionInt 版本数值
     * @return MultiBox产品对象
     */
    private PrepFullProductVO getPrepMultiBoxProduct(Long productId, Integer versionInt) {
        List<ProductMultibox> productMultiboxList = productMultiboxService.getListByProductAndVersionInt(productId, versionInt);
        PrepFullProductVO multiBox = new PrepFullProductVO();

        // 获取子产品
        List<Long> multiBoxIdList = StreamUtils.distinctMap(productMultiboxList, ProductMultibox::getId);
        Map<Long, List<ProductMultiboxDetail>> multiBoxDetailGroupByMultiBoxIdMap = productMultiboxDetailService.groupByMultiBoxIdList(multiBoxIdList);

        // MultiBox 子产品id
        List<Long> detailProductIdList = multiBoxDetailGroupByMultiBoxIdMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(ProductMultiboxDetail::getProductId)
                .distinct()
                .toList();
        List<Long> productIdList = new ArrayList<>(detailProductIdList);
        productIdList.add(productId);
        // 产品缓存获取
        Map<Long, BaseProductVO> baseProductMap = ProductCacheUtil.listByIds(productIdList)
                .stream()
                .map(obj -> BeanUtil.copyNew(obj, BaseProductVO.class))
                .collect(Collectors.toMap(BaseProductVO::getId, Function.identity()));
        // 封装MultiBox产品信息
        multiBox.setProductId(productId);
        multiBox.setBaseProductVO(baseProductMap.get(productId));
        multiBox.setMultiBoxList(productMultiboxList.stream()
                .map(obj -> {
                    // 设置MultiBox产品信息
                    PrepMultiBoxGroupVO box = new PrepMultiBoxGroupVO();
                    BeanUtil.copy(obj, box);
                    // 设置子产品明细
                    box.setDetailList(multiBoxDetailGroupByMultiBoxIdMap.getOrDefault(obj.getId(), Collections.emptyList())
                            .stream()
                            .map(o -> {
                                PrepMultiBoxDetailGroupVO detail = new PrepMultiBoxDetailGroupVO();
                                BeanUtil.copy(o, detail);
                                detail.setBaseProductVO(baseProductMap.get(o.getProductId()));
                                return detail;
                            })
                            .toList());
                    return box;
                })
                .toList());
        return multiBox;
    }

    /**
     * 获取Convert产品信息
     *
     * @param productId  产品id
     * @param versionInt 版本数值
     * @return Convert产品对象
     */
    @NotNull
    private PrepFullProductVO getPrepConvertProduct(Long productId, Integer versionInt) {
        List<ProductGroup> groupList = productGroupService.findOwnerGroupByProductIdAndVersionInt(productId, versionInt);
        PrepFullProductVO convert = new PrepFullProductVO();
        // 父产品id
        List<Long> parentProductIdList = StreamUtils.distinctMap(groupList, ProductGroup::getParentProductId);
        // 子产品id
        List<Long> childProductIdList = StreamUtils.distinctMap(groupList, ProductGroup::getChildProductId);
        Set<Long> productIdList = new HashSet<>(parentProductIdList);
        productIdList.addAll(childProductIdList);
        productIdList.add(productId);
        // 产品缓存获取
        Map<Long, BaseProductVO> baseProductMap = ProductCacheUtil.listByIds(productIdList)
                .stream()
                .map(obj -> BeanUtil.copyNew(obj, BaseProductVO.class))
                .collect(Collectors.toMap(BaseProductVO::getId, Function.identity()));
        // 设置产品id
        convert.setProductId(productId);
        convert.setBaseProductVO(baseProductMap.get(productId));
        convert.setGroupList(groupList.stream()
                .map(obj -> {
                    PrepGroupVO group = new PrepGroupVO();
                    BeanUtil.copy(obj, group);
                    group.setParentBaseProductVO(baseProductMap.get(group.getParentProductId()));
                    group.setChildBaseProductVO(baseProductMap.get(group.getChildProductId()));
                    return group;
                })
                .toList()
        );
        return convert;
    }

    /**
     * 获取PrePack产品信息
     *
     * @param productId  产品id
     * @param versionInt 版本数值
     * @return PrePack产品对象
     */
    private PrepFullProductVO getPrepPackProduct(Long productId, Integer versionInt) {
        List<ProductComponent> componentList = productComponentService.getListByAssemblyProductIdAndVersionInt(productId, versionInt);
        PrepFullProductVO component = new PrepFullProductVO();
        // 组件产品id
        List<Long> componentProductIdList = StreamUtils.distinctMap(componentList, ProductComponent::getComponentProductId);
        List<Long> productIdList = new ArrayList<>(componentProductIdList);
        productIdList.add(productId);
        // 产品缓存获取
        Map<Long, BaseProductVO> baseProductMap = ProductCacheUtil.listByIds(productIdList)
                .stream()
                .map(obj -> BeanUtil.copyNew(obj, BaseProductVO.class))
                .collect(Collectors.toMap(BaseProductVO::getId, Function.identity()));
        // 设置组件列表
        component.setComponentList(componentList.stream()
                .map(obj -> {
                    PrepComponentGroupVO recursionComponent = new PrepComponentGroupVO();
                    component.setAssemblyInstructionNote(obj.getAssemblyInstructionNote());
                    BeanUtil.copy(obj, recursionComponent);
                    recursionComponent.setBaseProductVO(baseProductMap.get(obj.getComponentProductId()));
                    return recursionComponent;
                })
                .toList()
        );
        // 设置组件产品id
        component.setProductId(productId);
        component.setBaseProductVO(baseProductMap.get(productId));
        return component;
    }
}
