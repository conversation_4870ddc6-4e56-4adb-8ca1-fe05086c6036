package cn.need.cloud.biz.model.vo.setting;

import cn.need.cloud.biz.model.vo.base.BaseSuperVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 全局序列号ref vo对象
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "全局序列号ref vo对象")
public class SequenceVO extends BaseSuperVO {


    /**
     * 序列类型
     */
    @Schema(description = "序列类型")
    private String sequenceType;

    /**
     * 序列代码
     */
    @Schema(description = "序列代码")
    private String code;

    /**
     * 序列ID
     */
    @Schema(description = "序列ID")
    private Long sequenceId;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Long version;

}