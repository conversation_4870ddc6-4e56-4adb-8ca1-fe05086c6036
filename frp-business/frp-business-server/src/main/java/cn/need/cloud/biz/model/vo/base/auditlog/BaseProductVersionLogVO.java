package cn.need.cloud.biz.model.vo.base.auditlog;

import cn.need.framework.common.core.lang.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = "产品 vo对象")
public class BaseProductVersionLogVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7888689056676234846L;

    /**
     * 唯一标识码
     */
    @Schema(description = "唯一标识码")
    private String refNum;

    /**
     * 供应商SKU
     */
    @Schema(description = "供应商SKU")
    private String supplierSku;

    /**
     * UPC码
     */
    @Schema(description = "UPC码")
    private String upc;

    /**
     * 产品版本号
     */
    @Schema(description = "产品版本号")
    private Integer productVersionInt;

    public String toLog() {
        return StringUtil.format("RefNum: {}, Sku: {}, UPC: {}, VersionInt: {}", refNum, supplierSku, upc, productVersionInt);
    }

}