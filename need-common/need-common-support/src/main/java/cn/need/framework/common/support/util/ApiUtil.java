package cn.need.framework.common.support.util;

import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.api.Result;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * do something in here.
 *
 * <AUTHOR>
 * @since 2021/4/1
 */
@Slf4j
@UtilityClass
public class ApiUtil {

    /**
     * 获取响应结果信息
     *
     * @param result 响应结果
     * @param <T>    结果中的数据泛型
     * @return T 结果中的数据信息
     */
    public static <T> T getResultData(Result<T> result) {
        Validate.notNull(result, "获取响应结果信息为空");
        if (!result.isSuccess()) {
            throw new BusinessException(result.getCode(), result.getMessage());
        }
        return result.getData();
    }

}
