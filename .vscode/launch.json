{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Launch with Arguments Prompt",
            "request": "launch",
            "mainClass": "",
            "args": "${command:SpecifyProgramArgs}"
        },
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "FrpApiCenterBootstrap",
            "request": "launch",
            "mainClass": "cn.need.cloud.apicenter.frp.FrpApiCenterBootstrap",
            "projectName": "frp-api-center-boot"
        },
        {
            "type": "java",
            "name": "FrpBaseBootstrap",
            "request": "launch",
            "mainClass": "cn.need.cloud.basic.FrpBaseBootstrap",
            "projectName": "frp-base-boot"
        },
        {
            "type": "java",
            "name": "FrpBusinessBootstrap",
            "request": "launch",
            "mainClass": "cn.need.cloud.biz.FrpBusinessBootstrap",
            "projectName": "frp-business-boot"
        },
        {
            "type": "java",
            "name": "RedisConfig",
            "request": "launch",
            "mainClass": "cn.need.cloud.biz.config.RedisConfig",
            "projectName": "frp-business-server"
        },
        {
            "type": "java",
            "name": "FrpGatewayBootstrap",
            "request": "launch",
            "mainClass": "cn.need.cloud.gateway.FrpGatewayBootstrap",
            "projectName": "frp-gateway"
        },
        {
            "type": "java",
            "name": "TestAttach",
            "request": "launch",
            "mainClass": "cn.need.cloud.dfs.controller.TestAttach",
            "projectName": "need-cloud-dfs-server"
        },
        {
            "type": "java",
            "name": "RedisTenantCacheRepertoryImpl",
            "request": "launch",
            "mainClass": "cn.need.cloud.upms.cache.impl.RedisTenantCacheRepertoryImpl",
            "projectName": "need-cloud-upms-cache"
        },
        {
            "type": "java",
            "name": "AuthProvider",
            "request": "launch",
            "mainClass": "cn.need.cloud.upms.provider.permissions.AuthProvider",
            "projectName": "need-cloud-upms-server"
        },
        {
            "type": "java",
            "name": "TestReflect",
            "request": "launch",
            "mainClass": "cn.need.framework.common.core.reflect.TestReflect",
            "projectName": "need-common-core"
        },
        {
            "type": "java",
            "name": "ChineseUtil",
            "request": "launch",
            "mainClass": "cn.need.framework.common.core.text.ChineseUtil",
            "projectName": "need-common-core"
        },
        {
            "type": "java",
            "name": "UneedMarketingGeneratorTest",
            "request": "launch",
            "mainClass": "cn.need.framework.common.generator.custom.UneedMarketingGeneratorTest",
            "projectName": "need-common-generator"
        },
        {
            "type": "java",
            "name": "UneedScrewGenerator",
            "request": "launch",
            "mainClass": "cn.need.framework.common.screw.UneedScrewGenerator",
            "projectName": "need-common-screw"
        }
    ]
}