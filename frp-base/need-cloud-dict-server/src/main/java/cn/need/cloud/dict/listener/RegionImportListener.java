package cn.need.cloud.dict.listener;

import cn.need.cloud.dict.model.entity.Region;
import cn.need.cloud.dict.model.vo.req.RegionImportReqVO;
import cn.need.cloud.dict.service.RegionService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.support.excel.AbstractImportListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.need.framework.common.core.lang.ObjectUtil.*;

/**
 * <AUTHOR>
 * @since 2024/1/23
 */
@Slf4j
public class RegionImportListener extends AbstractImportListener<RegionImportReqVO> {

    private final RegionService regionService;

    public RegionImportListener(RegionService regionService) {
        this.regionService = regionService;
    }

    @Override
    protected void doFinish(List<RegionImportReqVO> dataList) {
        if (isEmpty(dataList)) {
            return;
        }

        Map<String, RegionImportReqVO> regionMap = ObjectUtil.toMap(dataList, RegionImportReqVO::getRegionCode);
        handleDataList(dataList, regionMap);
        List<Region> regionList = BeanUtil.copyNew(dataList, Region.class);
        regionService.batchInsert(regionList);

        //刷新parentId，path, fullRegionName 为空的数据
        List<Region> regionList1 = regionService.list();
        List<Region> parentIdIsNullList = regionList1.stream().filter(region -> isNull(region.getParentId())).toList();
        Map<String, Region> regionMap1 = ObjectUtil.toMap(regionList1, Region::getRegionCode);
        handleParentIdIsNullList(parentIdIsNullList, regionMap1);
        regionService.batchUpdate(parentIdIsNullList);
    }

    @Override
    protected void doNext(RegionImportReqVO data, int index, List<String> messages) {
        log.info("data:{}", JsonUtil.toJson(data));
    }

    private void handleParentIdIsNullList(List<Region> parentIdIsNullList, Map<String, Region> regionMap1) {
        parentIdIsNullList.forEach(region -> {
            if (isNull(region.getFullRegionCode())) {
                return;
            }

            if (ObjectUtil.isNull(region.getParentRegionCode()) || ObjectUtil.equal(region.getParentRegionCode(), "0")) {
                region.setParentId(0L);
                region.setFullRegionName(region.getRegionName().concat(StringPool.COMMA));
                region.setPath(region.getId().toString().concat(StringPool.COMMA));
                return;
            }
            if (!regionMap1.containsKey(region.getRegionCode())) {
                return;
            }

            List<String> regionCodeList = Arrays.asList(region.getFullRegionCode().split(StringPool.COMMA));
            List<String> idList = Lists.arrayList();
            List<String> fullNameList = Lists.arrayList();
            regionCodeList.forEach(regionCode -> {
                if (regionMap1.containsKey(regionCode)) {
                    idList.add(regionMap1.get(regionCode).getId().toString());
                    fullNameList.add(regionMap1.get(regionCode).getRegionName());
                }
            });
            log.info("fullNameList={}", JsonUtil.toJson(fullNameList));
            log.info("idList={}", JsonUtil.toJson(idList));
            region.setParentId(regionMap1.get(region.getParentRegionCode()).getId());
            region.setFullRegionName(StringUtils.join(fullNameList, StringPool.COMMA));
            region.setPath(StringUtils.join(idList, StringPool.COMMA).concat(StringPool.COMMA));
        });
    }

    private void handleDataList(List<RegionImportReqVO> dataList, Map<String, RegionImportReqVO> regionMap) {
        double sorting = 1D;
        for (RegionImportReqVO data : dataList) {
            String codePath = null;

            if (isNull(data.getParentRegionCode()) || equal(data.getParentRegionCode(), "0")) {
                codePath = data.getRegionCode().concat(StringPool.COMMA);
            } else {
                codePath = data.getFullRegionCode().concat(StringPool.COMMA).concat(data.getRegionCode()).concat(StringPool.COMMA);
            }
            data.setFullRegionCode(codePath);
            data.setSorting(sorting);
            sorting += 1D;
        }
    }
}
