package cn.need.cloud.dict.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 行政区域 vo对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "行政区域 VO对象")
public class RegionTreePathVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 上级Id
     */
    @Schema(description = "上级Id")
    private String code;

    /**
     * path
     */
    @Schema(description = "path")
    private String path;

}