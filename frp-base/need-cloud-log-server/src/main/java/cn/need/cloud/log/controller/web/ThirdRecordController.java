package cn.need.cloud.log.controller.web;

import cn.need.cloud.log.converter.ThirdRecordConverter;
import cn.need.cloud.log.model.entity.ThirdRecord;
import cn.need.cloud.log.model.vo.ThirdRecordVO;
import cn.need.cloud.log.model.vo.req.ThirdRecordReqVO;
import cn.need.cloud.log.service.ThirdRecordService;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * do something in here.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/log/third-record")
@Tag(name = "业务系统请求日志记录信息管理")
@Slf4j
public class ThirdRecordController extends AbstractRestController<ThirdRecordService, ThirdRecord, ThirdRecordConverter, ThirdRecordVO> {


    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取数据列表接口", description = "根据传入参数条件，从数据库中获取分页后的数据列表")
    @PostMapping(value = "list")
    public Result<PageData<ThirdRecordVO>> list(@RequestBody @Parameter(description = "条件参数", required = true) PageSearch<ThirdRecordReqVO> search) {
        log.info("====> /api/log/third-record/list, search={}", JsonUtil.toJson(search));
        // 1. 缓存参数
        setParameterCache(search);
        // 2. 根据分页条件参数、实体类类型，构建分页对象
        Page<ThirdRecord> page = Conditions.page(search, ThirdRecord.class);
        //3. 构建查询条件
        ThirdRecordReqVO condition = ObjectUtil.nullToDefault(search.getCondition(), new ThirdRecordReqVO());
        //4. 优先根据条件，从数据库中获取数据
        List<ThirdRecordVO> list = service.listByThirdRecordCondition(page, condition);
        //5. 返回数据
        return success(new PageData<>(list, page));
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "清理登录日志接口", description = "根据条件，清理登录日志明细")
    @PostMapping({"login-log-clear"})
    public Result<Integer> loginLogClear(@RequestBody ThirdRecordReqVO condition) {
        log.info("====> /api/log/third-record/login-log-clear, condition={}", JsonUtil.toJson(condition));
        return success(service.deleteByThirdRecordCondition(condition));
    }
}
