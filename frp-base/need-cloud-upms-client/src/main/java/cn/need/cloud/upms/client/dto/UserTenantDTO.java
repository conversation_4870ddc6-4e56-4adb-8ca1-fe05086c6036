package cn.need.cloud.upms.client.dto;

import cn.need.framework.common.support.api.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 用户租户信息 dto对象
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserTenantDTO extends SuperDTO {


    /**
     * 用户id
     */
    private Long userId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 名字
     */
    private String firstName;

    /**
     * 姓氏
     */
    private String lastName;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 激活标志
     */
    private Boolean activeFlag;

    /**
     * 删除备注
     */
    private String deletedNote;

}